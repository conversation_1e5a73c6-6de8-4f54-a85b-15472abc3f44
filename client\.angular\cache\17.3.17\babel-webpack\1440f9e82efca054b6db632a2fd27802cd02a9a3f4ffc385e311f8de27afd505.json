{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class ProductService {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.endpoint = '/products';\n  }\n  /**\n   * Récupère tous les produits\n   */\n  getAllProducts(params) {\n    return this.apiService.get(this.endpoint, params);\n  }\n  /**\n   * Récupère un produit par son ID\n   */\n  getProductById(id) {\n    return this.apiService.get(`${this.endpoint}/${id}`);\n  }\n  /**\n   * Récupère toutes les catégories de produits\n   */\n  getCategories() {\n    return this.apiService.get(`${this.endpoint}/categories`);\n  }\n  /**\n   * Récupère les produits groupés par catégorie\n   */\n  getProductsByCategory() {\n    return this.apiService.get(`${this.endpoint}/by-category`);\n  }\n  /**\n   * Recherche des produits\n   */\n  searchProducts(params) {\n    return this.apiService.get(`${this.endpoint}/search`, params);\n  }\n  /**\n   * Filtre les produits par catégorie\n   */\n  getProductsBySpecificCategory(category) {\n    return this.getAllProducts({\n      category\n    });\n  }\n  /**\n   * Récupère les produits en stock uniquement\n   */\n  getAvailableProducts() {\n    return this.getAllProducts({\n      inStock: true\n    });\n  }\n  static {\n    this.ɵfac = function ProductService_Factory(t) {\n      return new (t || ProductService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProductService,\n      factory: ProductService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ProductService", "constructor", "apiService", "endpoint", "getAllProducts", "params", "get", "getProductById", "id", "getCategories", "getProductsByCategory", "searchProducts", "getProductsBySpecificCategory", "category", "getAvailableProducts", "inStock", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\services\\product.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { ApiService } from './api.service';\nimport { Product, ProductResponse, ProductSearchParams, ProductStatistics } from '../models/product.interface';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProductService {\n  private readonly endpoint = '/products';\n\n  constructor(private apiService: ApiService) {}\n\n  /**\n   * Récupère tous les produits\n   */\n  getAllProducts(params?: { category?: string; inStock?: boolean }): Observable<ProductResponse> {\n    return this.apiService.get<ProductResponse>(this.endpoint, params);\n  }\n\n  /**\n   * Récupère un produit par son ID\n   */\n  getProductById(id: string): Observable<ProductResponse> {\n    return this.apiService.get<ProductResponse>(`${this.endpoint}/${id}`);\n  }\n\n  /**\n   * Récupère toutes les catégories de produits\n   */\n  getCategories(): Observable<{ success: boolean; count: number; data: string[] }> {\n    return this.apiService.get<{ success: boolean; count: number; data: string[] }>(`${this.endpoint}/categories`);\n  }\n\n  /**\n   * Récupère les produits groupés par catégorie\n   */\n  getProductsByCategory(): Observable<{\n    success: boolean;\n    data: { [category: string]: Product[] };\n    statistics: ProductStatistics;\n  }> {\n    return this.apiService.get<{\n      success: boolean;\n      data: { [category: string]: Product[] };\n      statistics: ProductStatistics;\n    }>(`${this.endpoint}/by-category`);\n  }\n\n  /**\n   * Recherche des produits\n   */\n  searchProducts(params: ProductSearchParams): Observable<{\n    success: boolean;\n    query: string;\n    count: number;\n    data: Product[];\n  }> {\n    return this.apiService.get<{\n      success: boolean;\n      query: string;\n      count: number;\n      data: Product[];\n    }>(`${this.endpoint}/search`, params);\n  }\n\n  /**\n   * Filtre les produits par catégorie\n   */\n  getProductsBySpecificCategory(category: string): Observable<ProductResponse> {\n    return this.getAllProducts({ category });\n  }\n\n  /**\n   * Récupère les produits en stock uniquement\n   */\n  getAvailableProducts(): Observable<ProductResponse> {\n    return this.getAllProducts({ inStock: true });\n  }\n}\n"], "mappings": ";;AAQA,OAAM,MAAOA,cAAc;EAGzBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAFb,KAAAC,QAAQ,GAAG,WAAW;EAEM;EAE7C;;;EAGAC,cAAcA,CAACC,MAAiD;IAC9D,OAAO,IAAI,CAACH,UAAU,CAACI,GAAG,CAAkB,IAAI,CAACH,QAAQ,EAAEE,MAAM,CAAC;EACpE;EAEA;;;EAGAE,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACN,UAAU,CAACI,GAAG,CAAkB,GAAG,IAAI,CAACH,QAAQ,IAAIK,EAAE,EAAE,CAAC;EACvE;EAEA;;;EAGAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACP,UAAU,CAACI,GAAG,CAAsD,GAAG,IAAI,CAACH,QAAQ,aAAa,CAAC;EAChH;EAEA;;;EAGAO,qBAAqBA,CAAA;IAKnB,OAAO,IAAI,CAACR,UAAU,CAACI,GAAG,CAIvB,GAAG,IAAI,CAACH,QAAQ,cAAc,CAAC;EACpC;EAEA;;;EAGAQ,cAAcA,CAACN,MAA2B;IAMxC,OAAO,IAAI,CAACH,UAAU,CAACI,GAAG,CAKvB,GAAG,IAAI,CAACH,QAAQ,SAAS,EAAEE,MAAM,CAAC;EACvC;EAEA;;;EAGAO,6BAA6BA,CAACC,QAAgB;IAC5C,OAAO,IAAI,CAACT,cAAc,CAAC;MAAES;IAAQ,CAAE,CAAC;EAC1C;EAEA;;;EAGAC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACV,cAAc,CAAC;MAAEW,OAAO,EAAE;IAAI,CAAE,CAAC;EAC/C;;;uBAtEWf,cAAc,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdnB,cAAc;MAAAoB,OAAA,EAAdpB,cAAc,CAAAqB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}