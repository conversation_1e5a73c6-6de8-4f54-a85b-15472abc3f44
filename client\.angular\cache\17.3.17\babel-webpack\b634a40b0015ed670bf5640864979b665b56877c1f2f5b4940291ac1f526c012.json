{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function throwIfEmpty(errorFactory = defaultErrorFactory) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      subscriber.next(value);\n    }, () => hasValue ? subscriber.complete() : subscriber.error(errorFactory())));\n  });\n}\nfunction defaultErrorFactory() {\n  return new EmptyError();\n}", "map": {"version": 3, "names": ["EmptyError", "operate", "createOperatorSubscriber", "throwIfEmpty", "errorFactory", "defaultErrorFactory", "source", "subscriber", "hasValue", "subscribe", "value", "next", "complete", "error"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Site e-commerce/client/node_modules/rxjs/dist/esm/internal/operators/throwIfEmpty.js"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function throwIfEmpty(errorFactory = defaultErrorFactory) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            subscriber.next(value);\n        }, () => (hasValue ? subscriber.complete() : subscriber.error(errorFactory()))));\n    });\n}\nfunction defaultErrorFactory() {\n    return new EmptyError();\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,YAAYA,CAACC,YAAY,GAAGC,mBAAmB,EAAE;EAC7D,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ,GAAG,KAAK;IACpBF,MAAM,CAACG,SAAS,CAACP,wBAAwB,CAACK,UAAU,EAAGG,KAAK,IAAK;MAC7DF,QAAQ,GAAG,IAAI;MACfD,UAAU,CAACI,IAAI,CAACD,KAAK,CAAC;IAC1B,CAAC,EAAE,MAAOF,QAAQ,GAAGD,UAAU,CAACK,QAAQ,CAAC,CAAC,GAAGL,UAAU,CAACM,KAAK,CAACT,YAAY,CAAC,CAAC,CAAE,CAAC,CAAC;EACpF,CAAC,CAAC;AACN;AACA,SAASC,mBAAmBA,CAAA,EAAG;EAC3B,OAAO,IAAIL,UAAU,CAAC,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}