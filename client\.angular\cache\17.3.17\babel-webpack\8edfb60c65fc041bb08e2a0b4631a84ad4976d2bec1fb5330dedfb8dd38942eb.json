{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport class HeroComponent {\n  ngOnInit() {\n    // Animation d'entrée\n    setTimeout(() => {\n      const heroContent = document.querySelector('.hero-content');\n      if (heroContent) {\n        heroContent.classList.add('fade-in-up');\n      }\n    }, 100);\n  }\n  scrollToProducts() {\n    const productsSection = document.getElementById('products');\n    if (productsSection) {\n      productsSection.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }\n  scrollToServices() {\n    const servicesSection = document.getElementById('services');\n    if (servicesSection) {\n      servicesSection.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }\n  static {\n    this.ɵfac = function HeroComponent_Factory(t) {\n      return new (t || HeroComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeroComponent,\n      selectors: [[\"app-hero\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 62,\n      vars: 0,\n      consts: [[\"id\", \"hero\", 1, \"hero\"], [1, \"hero-background\"], [1, \"hero-overlay\"], [1, \"container\"], [1, \"hero-content\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"title-main\"], [1, \"title-subtitle\"], [1, \"hero-description\"], [1, \"hero-stats\"], [1, \"stat-item\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"hero-actions\"], [1, \"btn\", \"btn-primary\", \"btn-lg\", 3, \"click\"], [1, \"fas\", \"fa-cards-blank\"], [1, \"btn\", \"btn-outline\", \"btn-lg\", 3, \"click\"], [1, \"fas\", \"fa-user-tie\"], [1, \"hero-visual\"], [1, \"hero-image\"], [\"src\", \"/assets/images/hero-cards.jpg\", \"alt\", \"Cartes de d\\u00E9veloppement personnel Disconnect To Connect\", 1, \"main-image\"], [1, \"floating-card\", \"card-1\"], [1, \"card-content\"], [1, \"floating-card\", \"card-2\"], [1, \"floating-card\", \"card-3\"], [1, \"scroll-indicator\"], [1, \"scroll-arrow\"], [1, \"fas\", \"fa-chevron-down\"], [1, \"scroll-text\"]],\n      template: function HeroComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h1\", 6)(7, \"span\", 7);\n          i0.ɵɵtext(8, \"Disconnect To Connect\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"span\", 8);\n          i0.ɵɵtext(10, \"Red\\u00E9couvrez la connexion authentique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12, \" Lib\\u00E9rez-vous des distractions num\\u00E9riques et reconnectez-vous avec vous-m\\u00EAme et les autres gr\\u00E2ce \\u00E0 nos cartes de d\\u00E9veloppement personnel et services de coaching professionnels. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"span\", 12);\n          i0.ɵɵtext(16, \"8+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"span\", 13);\n          i0.ɵɵtext(18, \"Ann\\u00E9es d'exp\\u00E9rience\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 11)(20, \"span\", 12);\n          i0.ɵɵtext(21, \"500+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"span\", 13);\n          i0.ɵɵtext(23, \"Clients accompagn\\u00E9s\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 11)(25, \"span\", 12);\n          i0.ɵɵtext(26, \"12\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"span\", 13);\n          i0.ɵɵtext(28, \"Collections de cartes\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 14)(30, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function HeroComponent_Template_button_click_30_listener() {\n            return ctx.scrollToProducts();\n          });\n          i0.ɵɵelement(31, \"i\", 16);\n          i0.ɵɵtext(32, \" D\\u00E9couvrir nos cartes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function HeroComponent_Template_button_click_33_listener() {\n            return ctx.scrollToServices();\n          });\n          i0.ɵɵelement(34, \"i\", 18);\n          i0.ɵɵtext(35, \" Nos services \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 19)(37, \"div\", 20);\n          i0.ɵɵelement(38, \"img\", 21);\n          i0.ɵɵelementStart(39, \"div\", 22)(40, \"div\", 23)(41, \"h4\");\n          i0.ɵɵtext(42, \"Self-Love\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"D\\u00E9veloppez l'amour de soi\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 24)(46, \"div\", 23)(47, \"h4\");\n          i0.ɵɵtext(48, \"Gratitude\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"p\");\n          i0.ɵɵtext(50, \"Cultivez la reconnaissance\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 25)(52, \"div\", 23)(53, \"h4\");\n          i0.ɵɵtext(54, \"Confidence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"p\");\n          i0.ɵɵtext(56, \"Renforcez votre confiance\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(57, \"div\", 26)(58, \"div\", 27);\n          i0.ɵɵelement(59, \"i\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"span\", 29);\n          i0.ɵɵtext(61, \"D\\u00E9couvrez notre histoire\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [CommonModule, RouterModule],\n      styles: [\".hero[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  overflow: hidden;\\n  background: var(--gradient-accent);\\n}\\n\\n.hero-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url(\\\"/assets/images/hero-bg.jpg\\\") center/cover no-repeat;\\n  z-index: 1;\\n}\\n\\n.hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(243, 237, 224, 0.9) 0%, rgba(198, 174, 134, 0.8) 100%);\\n  z-index: 2;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 3;\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: var(--spacing-xxl);\\n  align-items: center;\\n  min-height: 80vh;\\n}\\n\\n.hero-text[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: translateY(30px);\\n  transition: var(--transition-slow);\\n}\\n.hero-text.fade-in-up[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-lg);\\n}\\n.hero-title[_ngcontent-%COMP%]   .title-main[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 3.5rem;\\n  font-weight: 700;\\n  background: var(--gradient-primary);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  line-height: 1.1;\\n  margin-bottom: var(--spacing-sm);\\n}\\n.hero-title[_ngcontent-%COMP%]   .title-subtitle[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 400;\\n  color: var(--text-light);\\n  font-family: \\\"Inter\\\", sans-serif;\\n}\\n\\n.hero-description[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  line-height: 1.7;\\n  color: var(--text-light);\\n  margin-bottom: var(--spacing-xl);\\n  max-width: 500px;\\n}\\n\\n.hero-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-lg);\\n  margin-bottom: var(--spacing-xl);\\n}\\n.hero-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.hero-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: var(--primary-color);\\n  font-family: \\\"Playfair Display\\\", serif;\\n}\\n.hero-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: var(--text-light);\\n  margin-top: var(--spacing-xs);\\n}\\n\\n.hero-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  flex-wrap: wrap;\\n}\\n.hero-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n}\\n.hero-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n.hero-visual[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.hero-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  max-width: 500px;\\n}\\n.hero-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  border-radius: var(--border-radius-xl);\\n  box-shadow: 0 20px 60px rgba(63, 25, 0, 0.2);\\n  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);\\n}\\n\\n.floating-card[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background: var(--white);\\n  border-radius: var(--border-radius-lg);\\n  padding: var(--spacing-md);\\n  box-shadow: 0 10px 30px rgba(63, 25, 0, 0.15);\\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;\\n}\\n.floating-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-bottom: var(--spacing-xs);\\n  color: var(--primary-color);\\n}\\n.floating-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--text-light);\\n  margin: 0;\\n}\\n.floating-card.card-1[_ngcontent-%COMP%] {\\n  top: 10%;\\n  right: -10%;\\n  animation-delay: 0s;\\n}\\n.floating-card.card-2[_ngcontent-%COMP%] {\\n  bottom: 20%;\\n  left: -15%;\\n  animation-delay: 2s;\\n}\\n.floating-card.card-3[_ngcontent-%COMP%] {\\n  top: 60%;\\n  right: -5%;\\n  animation-delay: 4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-20px);\\n  }\\n}\\n.scroll-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: var(--spacing-lg);\\n  left: 50%;\\n  transform: translateX(-50%);\\n  text-align: center;\\n  color: var(--text-light);\\n  z-index: 3;\\n}\\n.scroll-indicator[_ngcontent-%COMP%]   .scroll-arrow[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-xs);\\n  animation: _ngcontent-%COMP%_bounce 2s infinite;\\n}\\n.scroll-indicator[_ngcontent-%COMP%]   .scroll-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.scroll-indicator[_ngcontent-%COMP%]   .scroll-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  display: block;\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 50%, 80%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-10px);\\n  }\\n  60% {\\n    transform: translateY(-5px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .hero-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-xl);\\n    text-align: center;\\n  }\\n  .hero-title[_ngcontent-%COMP%]   .title-main[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .hero-stats[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .hero-actions[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .hero-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-width: 200px;\\n  }\\n  .floating-card[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .hero-title[_ngcontent-%COMP%]   .title-main[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .hero-title[_ngcontent-%COMP%]   .title-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .hero-stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n  }\\n  .hero-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .hero-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "HeroComponent", "ngOnInit", "setTimeout", "hero<PERSON><PERSON>nt", "document", "querySelector", "classList", "add", "scrollToProducts", "productsSection", "getElementById", "scrollIntoView", "behavior", "scrollToServices", "servicesSection", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HeroComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "HeroComponent_Template_button_click_30_listener", "HeroComponent_Template_button_click_33_listener", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\hero\\hero.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\hero\\hero.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'app-hero',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './hero.component.html',\n  styleUrls: ['./hero.component.scss']\n})\nexport class HeroComponent implements OnInit {\n  \n  ngOnInit(): void {\n    // Animation d'entrée\n    setTimeout(() => {\n      const heroContent = document.querySelector('.hero-content');\n      if (heroContent) {\n        heroContent.classList.add('fade-in-up');\n      }\n    }, 100);\n  }\n\n  scrollToProducts(): void {\n    const productsSection = document.getElementById('products');\n    if (productsSection) {\n      productsSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  }\n\n  scrollToServices(): void {\n    const servicesSection = document.getElementById('services');\n    if (servicesSection) {\n      servicesSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  }\n}\n", "<section class=\"hero\" id=\"hero\">\n  <div class=\"hero-background\">\n    <div class=\"hero-overlay\"></div>\n  </div>\n  \n  <div class=\"container\">\n    <div class=\"hero-content\">\n      <div class=\"hero-text\">\n        <h1 class=\"hero-title\">\n          <span class=\"title-main\">Disconnect To Connect</span>\n          <span class=\"title-subtitle\">Redécouvrez la connexion authentique</span>\n        </h1>\n        \n        <p class=\"hero-description\">\n          Libérez-vous des distractions numériques et reconnectez-vous avec vous-même et les autres \n          grâce à nos cartes de développement personnel et services de coaching professionnels.\n        </p>\n        \n        <div class=\"hero-stats\">\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">8+</span>\n            <span class=\"stat-label\">Années d'expérience</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">500+</span>\n            <span class=\"stat-label\">Clients accompagnés</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">12</span>\n            <span class=\"stat-label\">Collections de cartes</span>\n          </div>\n        </div>\n        \n        <div class=\"hero-actions\">\n          <button class=\"btn btn-primary btn-lg\" (click)=\"scrollToProducts()\">\n            <i class=\"fas fa-cards-blank\"></i>\n            Découvrir nos cartes\n          </button>\n          <button class=\"btn btn-outline btn-lg\" (click)=\"scrollToServices()\">\n            <i class=\"fas fa-user-tie\"></i>\n            Nos services\n          </button>\n        </div>\n      </div>\n      \n      <div class=\"hero-visual\">\n        <div class=\"hero-image\">\n          <img src=\"/assets/images/hero-cards.jpg\" alt=\"Cartes de développement personnel Disconnect To Connect\" \n               class=\"main-image\">\n          <div class=\"floating-card card-1\">\n            <div class=\"card-content\">\n              <h4>Self-Love</h4>\n              <p>Développez l'amour de soi</p>\n            </div>\n          </div>\n          <div class=\"floating-card card-2\">\n            <div class=\"card-content\">\n              <h4>Gratitude</h4>\n              <p>Cultivez la reconnaissance</p>\n            </div>\n          </div>\n          <div class=\"floating-card card-3\">\n            <div class=\"card-content\">\n              <h4>Confidence</h4>\n              <p>Renforcez votre confiance</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n  \n  <!-- Scroll indicator -->\n  <div class=\"scroll-indicator\">\n    <div class=\"scroll-arrow\">\n      <i class=\"fas fa-chevron-down\"></i>\n    </div>\n    <span class=\"scroll-text\">Découvrez notre histoire</span>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;AAS9C,OAAM,MAAOC,aAAa;EAExBC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAC;MAC3D,IAAIF,WAAW,EAAE;QACfA,WAAW,CAACG,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;;IAE3C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,gBAAgBA,CAAA;IACd,MAAMC,eAAe,GAAGL,QAAQ,CAACM,cAAc,CAAC,UAAU,CAAC;IAC3D,IAAID,eAAe,EAAE;MACnBA,eAAe,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;;EAE1D;EAEAC,gBAAgBA,CAAA;IACd,MAAMC,eAAe,GAAGV,QAAQ,CAACM,cAAc,CAAC,UAAU,CAAC;IAC3D,IAAII,eAAe,EAAE;MACnBA,eAAe,CAACH,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;;EAE1D;;;uBAxBWZ,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAe,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVxBP,EADF,CAAAS,cAAA,iBAAgC,aACD;UAC3BT,EAAA,CAAAU,SAAA,aAAgC;UAClCV,EAAA,CAAAW,YAAA,EAAM;UAMEX,EAJR,CAAAS,cAAA,aAAuB,aACK,aACD,YACE,cACI;UAAAT,EAAA,CAAAY,MAAA,4BAAqB;UAAAZ,EAAA,CAAAW,YAAA,EAAO;UACrDX,EAAA,CAAAS,cAAA,cAA6B;UAAAT,EAAA,CAAAY,MAAA,iDAAoC;UACnEZ,EADmE,CAAAW,YAAA,EAAO,EACrE;UAELX,EAAA,CAAAS,cAAA,YAA4B;UAC1BT,EAAA,CAAAY,MAAA,uNAEF;UAAAZ,EAAA,CAAAW,YAAA,EAAI;UAIAX,EAFJ,CAAAS,cAAA,eAAwB,eACC,gBACK;UAAAT,EAAA,CAAAY,MAAA,UAAE;UAAAZ,EAAA,CAAAW,YAAA,EAAO;UACnCX,EAAA,CAAAS,cAAA,gBAAyB;UAAAT,EAAA,CAAAY,MAAA,qCAAmB;UAC9CZ,EAD8C,CAAAW,YAAA,EAAO,EAC/C;UAEJX,EADF,CAAAS,cAAA,eAAuB,gBACK;UAAAT,EAAA,CAAAY,MAAA,YAAI;UAAAZ,EAAA,CAAAW,YAAA,EAAO;UACrCX,EAAA,CAAAS,cAAA,gBAAyB;UAAAT,EAAA,CAAAY,MAAA,gCAAmB;UAC9CZ,EAD8C,CAAAW,YAAA,EAAO,EAC/C;UAEJX,EADF,CAAAS,cAAA,eAAuB,gBACK;UAAAT,EAAA,CAAAY,MAAA,UAAE;UAAAZ,EAAA,CAAAW,YAAA,EAAO;UACnCX,EAAA,CAAAS,cAAA,gBAAyB;UAAAT,EAAA,CAAAY,MAAA,6BAAqB;UAElDZ,EAFkD,CAAAW,YAAA,EAAO,EACjD,EACF;UAGJX,EADF,CAAAS,cAAA,eAA0B,kBAC4C;UAA7BT,EAAA,CAAAa,UAAA,mBAAAC,gDAAA;YAAA,OAASN,GAAA,CAAAlB,gBAAA,EAAkB;UAAA,EAAC;UACjEU,EAAA,CAAAU,SAAA,aAAkC;UAClCV,EAAA,CAAAY,MAAA,mCACF;UAAAZ,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAS,cAAA,kBAAoE;UAA7BT,EAAA,CAAAa,UAAA,mBAAAE,gDAAA;YAAA,OAASP,GAAA,CAAAb,gBAAA,EAAkB;UAAA,EAAC;UACjEK,EAAA,CAAAU,SAAA,aAA+B;UAC/BV,EAAA,CAAAY,MAAA,sBACF;UAEJZ,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;UAGJX,EADF,CAAAS,cAAA,eAAyB,eACC;UACtBT,EAAA,CAAAU,SAAA,eACwB;UAGpBV,EAFJ,CAAAS,cAAA,eAAkC,eACN,UACpB;UAAAT,EAAA,CAAAY,MAAA,iBAAS;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UAClBX,EAAA,CAAAS,cAAA,SAAG;UAAAT,EAAA,CAAAY,MAAA,sCAAyB;UAEhCZ,EAFgC,CAAAW,YAAA,EAAI,EAC5B,EACF;UAGFX,EAFJ,CAAAS,cAAA,eAAkC,eACN,UACpB;UAAAT,EAAA,CAAAY,MAAA,iBAAS;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UAClBX,EAAA,CAAAS,cAAA,SAAG;UAAAT,EAAA,CAAAY,MAAA,kCAA0B;UAEjCZ,EAFiC,CAAAW,YAAA,EAAI,EAC7B,EACF;UAGFX,EAFJ,CAAAS,cAAA,eAAkC,eACN,UACpB;UAAAT,EAAA,CAAAY,MAAA,kBAAU;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACnBX,EAAA,CAAAS,cAAA,SAAG;UAAAT,EAAA,CAAAY,MAAA,iCAAyB;UAMxCZ,EANwC,CAAAW,YAAA,EAAI,EAC5B,EACF,EACF,EACF,EACF,EACF;UAIJX,EADF,CAAAS,cAAA,eAA8B,eACF;UACxBT,EAAA,CAAAU,SAAA,aAAmC;UACrCV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAS,cAAA,gBAA0B;UAAAT,EAAA,CAAAY,MAAA,qCAAwB;UAEtDZ,EAFsD,CAAAW,YAAA,EAAO,EACrD,EACE;;;qBDxEE/B,YAAY,EAAEC,YAAY;MAAAmC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}