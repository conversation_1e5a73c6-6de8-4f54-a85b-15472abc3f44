import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ProductService } from '../../services/product.service';
import { Product } from '../../models/product.interface';

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <section class="products-section" [class.home-section]="isHomePage">
      <div class="container">
        <h2>{{ isHomePage ? 'Nos Cartes de Développement Personnel' : 'Toutes nos Cartes' }}</h2>
        <p class="section-description" *ngIf="!isHomePage">
          Découvrez notre collection complète de cartes de développement personnel
        </p>
        
        <div class="products-grid">
          <div class="product-card" *ngFor="let product of displayedProducts">
            <div class="product-image">
              <img [src]="product.image" [alt]="product.name" />
            </div>
            <div class="product-content">
              <h3>{{ product.name }}</h3>
              <p class="product-description">{{ product.description }}</p>
              <div class="product-price">{{ product.price }}€</div>
              <button 
                class="btn btn-primary"
                [routerLink]="['/products', product.id]"
              >
                Voir les détails
              </button>
            </div>
          </div>
        </div>
        
        <div class="text-center" *ngIf="isHomePage && products.length > 3">
          <button class="btn btn-outline" routerLink="/products">
            Voir tous nos produits
          </button>
        </div>
      </div>
    </section>
  `,
  styles: [`
    .products-section {
      padding: 80px 0;
      background: #f8f9fa;
    }
    
    .home-section {
      padding: 60px 0;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    h2 {
      text-align: center;
      margin-bottom: 20px;
      color: #2c3e50;
      font-size: 2.5rem;
    }
    
    .section-description {
      text-align: center;
      margin-bottom: 40px;
      color: #666;
      font-size: 1.1rem;
    }
    
    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
      margin-bottom: 40px;
    }
    
    .product-card {
      background: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }
    
    .product-card:hover {
      transform: translateY(-5px);
    }
    
    .product-image {
      height: 200px;
      overflow: hidden;
    }
    
    .product-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .product-content {
      padding: 20px;
    }
    
    .product-content h3 {
      margin-bottom: 10px;
      color: #2c3e50;
    }
    
    .product-description {
      color: #666;
      margin-bottom: 15px;
      line-height: 1.6;
    }
    
    .product-price {
      font-size: 1.5rem;
      font-weight: bold;
      color: #e74c3c;
      margin-bottom: 15px;
    }
    
    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      text-align: center;
      transition: all 0.3s ease;
    }
    
    .btn-primary {
      background: #3498db;
      color: white;
    }
    
    .btn-primary:hover {
      background: #2980b9;
    }
    
    .btn-outline {
      background: transparent;
      color: #3498db;
      border: 2px solid #3498db;
    }
    
    .btn-outline:hover {
      background: #3498db;
      color: white;
    }
    
    .text-center {
      text-align: center;
    }
  `]
})
export class ProductsComponent implements OnInit {
  @Input() isHomePage: boolean = false;
  
  products: Product[] = [];
  displayedProducts: Product[] = [];

  constructor(private productService: ProductService) {}

  ngOnInit(): void {
    this.loadProducts();
  }

  private loadProducts(): void {
    this.productService.getProducts().subscribe({
      next: (products: Product[]) => {
        this.products = products;
        this.displayedProducts = this.isHomePage
          ? products.slice(0, 3)
          : products;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des produits:', error);
        // Fallback avec des données de démonstration
        this.products = this.getDemoProducts();
        this.displayedProducts = this.isHomePage
          ? this.products.slice(0, 3)
          : this.products;
      }
    });
  }

  private getDemoProducts(): Product[] {
    return [
      {
        id: '1',
        name: 'Cartes Confiance en Soi',
        description: 'Un jeu de cartes pour développer votre confiance et votre estime de soi.',
        price: 29.99,
        image: '/assets/images/cards-confidence.jpg',
        category: 'Développement Personnel',
        inStock: true,
        formattedPrice: '29,99€',
        tags: ['confiance', 'estime de soi'],
        available: true,
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Cartes Méditation',
        description: 'Découvrez différentes techniques de méditation avec ces cartes guidées.',
        price: 24.99,
        image: '/assets/images/cards-meditation.jpg',
        category: 'Bien-être',
        inStock: true,
        formattedPrice: '24,99€',
        tags: ['méditation', 'bien-être'],
        available: true,
        createdAt: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Cartes Gratitude',
        description: 'Cultivez la gratitude au quotidien avec ces exercices pratiques.',
        price: 19.99,
        image: '/assets/images/cards-gratitude.jpg',
        category: 'Développement Personnel',
        inStock: true,
        formattedPrice: '19,99€',
        tags: ['gratitude', 'développement'],
        available: true,
        createdAt: new Date().toISOString()
      }
    ];
  }
}
