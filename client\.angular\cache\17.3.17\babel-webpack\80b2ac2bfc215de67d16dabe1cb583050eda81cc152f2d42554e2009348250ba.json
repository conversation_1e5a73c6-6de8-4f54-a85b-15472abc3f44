{"ast": null, "code": "import { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nexport class AsyncAction extends Action {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.pending = false;\n  }\n  schedule(state, delay = 0) {\n    var _a;\n    if (this.closed) {\n      return this;\n    }\n    this.state = state;\n    const id = this.id;\n    const scheduler = this.scheduler;\n    if (id != null) {\n      this.id = this.recycleAsyncId(scheduler, id, delay);\n    }\n    this.pending = true;\n    this.delay = delay;\n    this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n    return this;\n  }\n  requestAsyncId(scheduler, _id, delay = 0) {\n    return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n  }\n  recycleAsyncId(_scheduler, id, delay = 0) {\n    if (delay != null && this.delay === delay && this.pending === false) {\n      return id;\n    }\n    if (id != null) {\n      intervalProvider.clearInterval(id);\n    }\n    return undefined;\n  }\n  execute(state, delay) {\n    if (this.closed) {\n      return new Error('executing a cancelled action');\n    }\n    this.pending = false;\n    const error = this._execute(state, delay);\n    if (error) {\n      return error;\n    } else if (this.pending === false && this.id != null) {\n      this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n    }\n  }\n  _execute(state, _delay) {\n    let errored = false;\n    let errorValue;\n    try {\n      this.work(state);\n    } catch (e) {\n      errored = true;\n      errorValue = e ? e : new Error('Scheduled action threw falsy error');\n    }\n    if (errored) {\n      this.unsubscribe();\n      return errorValue;\n    }\n  }\n  unsubscribe() {\n    if (!this.closed) {\n      const {\n        id,\n        scheduler\n      } = this;\n      const {\n        actions\n      } = scheduler;\n      this.work = this.state = this.scheduler = null;\n      this.pending = false;\n      arrRemove(actions, this);\n      if (id != null) {\n        this.id = this.recycleAsyncId(scheduler, id, null);\n      }\n      this.delay = null;\n      super.unsubscribe();\n    }\n  }\n}", "map": {"version": 3, "names": ["Action", "intervalProvider", "arr<PERSON><PERSON><PERSON>", "AsyncAction", "constructor", "scheduler", "work", "pending", "schedule", "state", "delay", "_a", "closed", "id", "recycleAsyncId", "requestAsyncId", "_id", "setInterval", "flush", "bind", "_scheduler", "clearInterval", "undefined", "execute", "Error", "error", "_execute", "_delay", "errored", "errorValue", "e", "unsubscribe", "actions"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Site e-commerce/client/node_modules/rxjs/dist/esm/internal/scheduler/AsyncAction.js"], "sourcesContent": ["import { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nexport class AsyncAction extends Action {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n        this.pending = false;\n    }\n    schedule(state, delay = 0) {\n        var _a;\n        if (this.closed) {\n            return this;\n        }\n        this.state = state;\n        const id = this.id;\n        const scheduler = this.scheduler;\n        if (id != null) {\n            this.id = this.recycleAsyncId(scheduler, id, delay);\n        }\n        this.pending = true;\n        this.delay = delay;\n        this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n        return this;\n    }\n    requestAsyncId(scheduler, _id, delay = 0) {\n        return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n    }\n    recycleAsyncId(_scheduler, id, delay = 0) {\n        if (delay != null && this.delay === delay && this.pending === false) {\n            return id;\n        }\n        if (id != null) {\n            intervalProvider.clearInterval(id);\n        }\n        return undefined;\n    }\n    execute(state, delay) {\n        if (this.closed) {\n            return new Error('executing a cancelled action');\n        }\n        this.pending = false;\n        const error = this._execute(state, delay);\n        if (error) {\n            return error;\n        }\n        else if (this.pending === false && this.id != null) {\n            this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n        }\n    }\n    _execute(state, _delay) {\n        let errored = false;\n        let errorValue;\n        try {\n            this.work(state);\n        }\n        catch (e) {\n            errored = true;\n            errorValue = e ? e : new Error('Scheduled action threw falsy error');\n        }\n        if (errored) {\n            this.unsubscribe();\n            return errorValue;\n        }\n    }\n    unsubscribe() {\n        if (!this.closed) {\n            const { id, scheduler } = this;\n            const { actions } = scheduler;\n            this.work = this.state = this.scheduler = null;\n            this.pending = false;\n            arrRemove(actions, this);\n            if (id != null) {\n                this.id = this.recycleAsyncId(scheduler, id, null);\n            }\n            this.delay = null;\n            super.unsubscribe();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,OAAO,MAAMC,WAAW,SAASH,MAAM,CAAC;EACpCI,WAAWA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACzB,KAAK,CAACD,SAAS,EAAEC,IAAI,CAAC;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACAC,QAAQA,CAACC,KAAK,EAAEC,KAAK,GAAG,CAAC,EAAE;IACvB,IAAIC,EAAE;IACN,IAAI,IAAI,CAACC,MAAM,EAAE;MACb,OAAO,IAAI;IACf;IACA,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,MAAMI,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,MAAMR,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIQ,EAAE,IAAI,IAAI,EAAE;MACZ,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAACT,SAAS,EAAEQ,EAAE,EAAEH,KAAK,CAAC;IACvD;IACA,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,IAAI,CAACG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,EAAE,GAAG,CAACF,EAAE,GAAG,IAAI,CAACE,EAAE,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACI,cAAc,CAACV,SAAS,EAAE,IAAI,CAACQ,EAAE,EAAEH,KAAK,CAAC;IACxG,OAAO,IAAI;EACf;EACAK,cAAcA,CAACV,SAAS,EAAEW,GAAG,EAAEN,KAAK,GAAG,CAAC,EAAE;IACtC,OAAOT,gBAAgB,CAACgB,WAAW,CAACZ,SAAS,CAACa,KAAK,CAACC,IAAI,CAACd,SAAS,EAAE,IAAI,CAAC,EAAEK,KAAK,CAAC;EACrF;EACAI,cAAcA,CAACM,UAAU,EAAEP,EAAE,EAAEH,KAAK,GAAG,CAAC,EAAE;IACtC,IAAIA,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,KAAKA,KAAK,IAAI,IAAI,CAACH,OAAO,KAAK,KAAK,EAAE;MACjE,OAAOM,EAAE;IACb;IACA,IAAIA,EAAE,IAAI,IAAI,EAAE;MACZZ,gBAAgB,CAACoB,aAAa,CAACR,EAAE,CAAC;IACtC;IACA,OAAOS,SAAS;EACpB;EACAC,OAAOA,CAACd,KAAK,EAAEC,KAAK,EAAE;IAClB,IAAI,IAAI,CAACE,MAAM,EAAE;MACb,OAAO,IAAIY,KAAK,CAAC,8BAA8B,CAAC;IACpD;IACA,IAAI,CAACjB,OAAO,GAAG,KAAK;IACpB,MAAMkB,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACjB,KAAK,EAAEC,KAAK,CAAC;IACzC,IAAIe,KAAK,EAAE;MACP,OAAOA,KAAK;IAChB,CAAC,MACI,IAAI,IAAI,CAAClB,OAAO,KAAK,KAAK,IAAI,IAAI,CAACM,EAAE,IAAI,IAAI,EAAE;MAChD,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACT,SAAS,EAAE,IAAI,CAACQ,EAAE,EAAE,IAAI,CAAC;IAChE;EACJ;EACAa,QAAQA,CAACjB,KAAK,EAAEkB,MAAM,EAAE;IACpB,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIC,UAAU;IACd,IAAI;MACA,IAAI,CAACvB,IAAI,CAACG,KAAK,CAAC;IACpB,CAAC,CACD,OAAOqB,CAAC,EAAE;MACNF,OAAO,GAAG,IAAI;MACdC,UAAU,GAAGC,CAAC,GAAGA,CAAC,GAAG,IAAIN,KAAK,CAAC,oCAAoC,CAAC;IACxE;IACA,IAAII,OAAO,EAAE;MACT,IAAI,CAACG,WAAW,CAAC,CAAC;MAClB,OAAOF,UAAU;IACrB;EACJ;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAE;MACd,MAAM;QAAEC,EAAE;QAAER;MAAU,CAAC,GAAG,IAAI;MAC9B,MAAM;QAAE2B;MAAQ,CAAC,GAAG3B,SAAS;MAC7B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACG,KAAK,GAAG,IAAI,CAACJ,SAAS,GAAG,IAAI;MAC9C,IAAI,CAACE,OAAO,GAAG,KAAK;MACpBL,SAAS,CAAC8B,OAAO,EAAE,IAAI,CAAC;MACxB,IAAInB,EAAE,IAAI,IAAI,EAAE;QACZ,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAACT,SAAS,EAAEQ,EAAE,EAAE,IAAI,CAAC;MACtD;MACA,IAAI,CAACH,KAAK,GAAG,IAAI;MACjB,KAAK,CAACqB,WAAW,CAAC,CAAC;IACvB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}