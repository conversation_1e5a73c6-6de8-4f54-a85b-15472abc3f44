{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/product.service\";\nimport * as i3 from \"@angular/common\";\nfunction ProductDetailComponent_div_0_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"h3\");\n    i0.ɵɵtext(2, \"Description compl\\u00E8te\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \" Ce jeu de cartes de d\\u00E9veloppement personnel a \\u00E9t\\u00E9 con\\u00E7u pour vous accompagner dans votre parcours de croissance personnelle. Chaque carte contient des exercices pratiques, des r\\u00E9flexions profondes et des conseils inspirants pour vous aider \\u00E0 d\\u00E9velopper votre potentiel. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \" Que vous soyez d\\u00E9butant ou exp\\u00E9riment\\u00E9 dans le d\\u00E9veloppement personnel, ces cartes s'adaptent \\u00E0 votre rythme et \\u00E0 vos besoins sp\\u00E9cifiques. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProductDetailComponent_div_0_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"h3\");\n    i0.ɵɵtext(2, \"Comment utiliser vos cartes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ol\")(4, \"li\");\n    i0.ɵɵtext(5, \"Choisissez un moment calme de votre journ\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"li\");\n    i0.ɵɵtext(7, \"M\\u00E9langez les cartes et tirez-en une au hasard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"li\");\n    i0.ɵɵtext(9, \"Lisez attentivement le contenu de la carte\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"li\");\n    i0.ɵɵtext(11, \"Prenez le temps de r\\u00E9fl\\u00E9chir \\u00E0 la question ou l'exercice propos\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"li\");\n    i0.ɵɵtext(13, \"Mettez en pratique les conseils dans votre quotidien\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProductDetailComponent_div_0_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"h3\");\n    i0.ɵɵtext(2, \"Avis de nos clients\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27)(4, \"div\", 28);\n    i0.ɵɵtext(5, \"\\u2605\\u2605\\u2605\\u2605\\u2605\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"\\\"Un outil formidable pour le d\\u00E9veloppement personnel. Je recommande vivement !\\\"\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"cite\");\n    i0.ɵɵtext(9, \"- Marie L.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 27)(11, \"div\", 28);\n    i0.ɵɵtext(12, \"\\u2605\\u2605\\u2605\\u2605\\u2606\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14, \"\\\"Tr\\u00E8s bien con\\u00E7u, les exercices sont vari\\u00E9s et inspirants.\\\"\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"cite\");\n    i0.ɵɵtext(16, \"- Pierre D.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProductDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"nav\", 5)(3, \"a\", 6);\n    i0.ɵɵtext(4, \"Accueil\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 7);\n    i0.ɵɵtext(8, \"Produits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9);\n    i0.ɵɵelement(15, \"img\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 11)(17, \"h1\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\", 12);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\", 13);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 14)(24, \"h3\");\n    i0.ɵɵtext(25, \"Caract\\u00E9ristiques\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"ul\")(27, \"li\");\n    i0.ɵɵtext(28, \"Jeu de cartes de d\\u00E9veloppement personnel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"li\");\n    i0.ɵɵtext(30, \"Format pratique et transportable\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"li\");\n    i0.ɵɵtext(32, \"Illustrations inspirantes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"li\");\n    i0.ɵɵtext(34, \"Guide d'utilisation inclus\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 15)(36, \"span\", 16);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 17);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 18)(41, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 20);\n    i0.ɵɵtext(44, \" Retour aux produits \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(45, \"div\", 21)(46, \"div\", 22)(47, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"description\");\n    });\n    i0.ɵɵtext(48, \" Description d\\u00E9taill\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"usage\");\n    });\n    i0.ɵɵtext(50, \" Mode d'emploi \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"reviews\");\n    });\n    i0.ɵɵtext(52, \" Avis clients \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 24);\n    i0.ɵɵtemplate(54, ProductDetailComponent_div_0_div_54_Template, 7, 0, \"div\", 25)(55, ProductDetailComponent_div_0_div_55_Template, 14, 0, \"div\", 25)(56, ProductDetailComponent_div_0_div_56_Template, 17, 0, \"div\", 25);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r1.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.product.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.product.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.product.description);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.product.price, \"\\u20AC\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"in-stock\", ctx_r1.product.inStock)(\"out-of-stock\", !ctx_r1.product.inStock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.product.inStock ? \"En stock\" : \"Rupture de stock\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.product.inStock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.product.inStock ? \"Ajouter au panier\" : \"Produit indisponible\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"description\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"usage\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"reviews\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"description\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"usage\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"reviews\");\n  }\n}\nfunction ProductDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"p\");\n    i0.ɵɵtext(2, \"Chargement du produit...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProductDetailComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 4)(2, \"h2\");\n    i0.ɵɵtext(3, \"Produit non trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Le produit que vous recherchez n'existe pas ou n'est plus disponible.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 31);\n    i0.ɵɵtext(7, \" Retour aux produits \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class ProductDetailComponent {\n  constructor(route, router, productService) {\n    this.route = route;\n    this.router = router;\n    this.productService = productService;\n    this.product = null;\n    this.error = false;\n    this.activeTab = 'description';\n  }\n  ngOnInit() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.loadProduct(id);\n    } else {\n      this.error = true;\n    }\n  }\n  loadProduct(id) {\n    this.productService.getProduct(id).subscribe({\n      next: product => {\n        this.product = product;\n      },\n      error: () => {\n        this.error = true;\n      }\n    });\n  }\n  addToCart() {\n    if (this.product) {\n      // Logique d'ajout au panier à implémenter\n      alert(`${this.product.name} ajouté au panier !`);\n    }\n  }\n  static {\n    this.ɵfac = function ProductDetailComponent_Factory(t) {\n      return new (t || ProductDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProductDetailComponent,\n      selectors: [[\"app-product-detail\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"product-detail\", 4, \"ngIf\"], [\"class\", \"loading\", 4, \"ngIf\"], [\"class\", \"error\", 4, \"ngIf\"], [1, \"product-detail\"], [1, \"container\"], [1, \"breadcrumb\"], [\"routerLink\", \"/\"], [\"routerLink\", \"/products\"], [1, \"product-content\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-category\"], [1, \"product-description\"], [1, \"product-details\"], [1, \"product-price\"], [1, \"price\"], [1, \"stock-status\"], [1, \"product-actions\"], [1, \"btn\", \"btn-primary\", \"btn-large\", 3, \"click\", \"disabled\"], [\"routerLink\", \"/products\", 1, \"btn\", \"btn-outline\"], [1, \"product-tabs\"], [1, \"tab-headers\"], [1, \"tab-header\", 3, \"click\"], [1, \"tab-content\"], [\"class\", \"tab-panel\", 4, \"ngIf\"], [1, \"tab-panel\"], [1, \"review\"], [1, \"review-rating\"], [1, \"loading\"], [1, \"error\"], [\"routerLink\", \"/products\", 1, \"btn\", \"btn-primary\"]],\n      template: function ProductDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ProductDetailComponent_div_0_Template, 57, 23, \"div\", 0)(1, ProductDetailComponent_div_1_Template, 3, 0, \"div\", 1)(2, ProductDetailComponent_div_2_Template, 8, 0, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.product);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.product && !ctx.error);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, RouterModule, i1.RouterLink],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.product-detail[_ngcontent-%COMP%] {\\n  padding: 100px 0 80px;\\n  min-height: 100vh;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  color: #666;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #3498db;\\n  text-decoration: none;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin: 0 10px;\\n}\\n\\n.product-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 40px;\\n  margin-bottom: 60px;\\n}\\n\\n@media (max-width: 768px) {\\n  .product-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 10px;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.product-category[_ngcontent-%COMP%] {\\n  color: #3498db;\\n  font-weight: 500;\\n  margin-bottom: 20px;\\n}\\n\\n.product-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.6;\\n  margin-bottom: 30px;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 15px;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 5px 0;\\n  color: #666;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before {\\n  content: \\\"\\u2713\\\";\\n  color: #27ae60;\\n  margin-right: 10px;\\n}\\n\\n.product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  color: #e74c3c;\\n}\\n\\n.stock-status[_ngcontent-%COMP%] {\\n  padding: 5px 15px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.in-stock[_ngcontent-%COMP%] {\\n  background: #d5f4e6;\\n  color: #27ae60;\\n}\\n\\n.out-of-stock[_ngcontent-%COMP%] {\\n  background: #fadbd8;\\n  color: #e74c3c;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  flex-wrap: wrap;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  text-decoration: none;\\n  display: inline-block;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  font-size: 16px;\\n}\\n\\n.btn-large[_ngcontent-%COMP%] {\\n  padding: 15px 30px;\\n  font-size: 18px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #2980b9;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #3498db;\\n  border: 2px solid #3498db;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%]:hover {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.product-tabs[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n\\n.tab-headers[_ngcontent-%COMP%] {\\n  display: flex;\\n  background: #f8f9fa;\\n}\\n\\n.tab-header[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 15px 20px;\\n  border: none;\\n  background: transparent;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.tab-header.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #3498db;\\n  border-bottom: 2px solid #3498db;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n.review[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.review-rating[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n  margin-bottom: 10px;\\n}\\n\\n.review[_ngcontent-%COMP%]   cite[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-style: italic;\\n}\\n\\n.loading[_ngcontent-%COMP%], .error[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 100px 0;\\n}\\n\\n.error[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  margin-bottom: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "ProductDetailComponent_div_0_Template_button_click_41_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "addToCart", "ProductDetailComponent_div_0_Template_button_click_47_listener", "activeTab", "ProductDetailComponent_div_0_Template_button_click_49_listener", "ProductDetailComponent_div_0_Template_button_click_51_listener", "ɵɵtemplate", "ProductDetailComponent_div_0_div_54_Template", "ProductDetailComponent_div_0_div_55_Template", "ProductDetailComponent_div_0_div_56_Template", "ɵɵadvance", "ɵɵtextInterpolate", "product", "name", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "category", "description", "ɵɵtextInterpolate1", "price", "ɵɵclassProp", "inStock", "ProductDetailComponent", "constructor", "route", "router", "productService", "error", "ngOnInit", "id", "snapshot", "paramMap", "get", "loadProduct", "getProduct", "subscribe", "next", "alert", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProductService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProductDetailComponent_Template", "rf", "ctx", "ProductDetailComponent_div_0_Template", "ProductDetailComponent_div_1_Template", "ProductDetailComponent_div_2_Template", "i3", "NgIf", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\product-detail\\product-detail.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { ProductService } from '../../services/product.service';\nimport { Product } from '../../models/product.interface';\n\n@Component({\n  selector: 'app-product-detail',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <div class=\"product-detail\" *ngIf=\"product\">\n      <div class=\"container\">\n        <nav class=\"breadcrumb\">\n          <a routerLink=\"/\">Accueil</a>\n          <span>/</span>\n          <a routerLink=\"/products\">Produits</a>\n          <span>/</span>\n          <span>{{ product.name }}</span>\n        </nav>\n        \n        <div class=\"product-content\">\n          <div class=\"product-image\">\n            <img [src]=\"product.image\" [alt]=\"product.name\" />\n          </div>\n          \n          <div class=\"product-info\">\n            <h1>{{ product.name }}</h1>\n            <p class=\"product-category\">{{ product.category }}</p>\n            <p class=\"product-description\">{{ product.description }}</p>\n            \n            <div class=\"product-details\">\n              <h3>Caractéristiques</h3>\n              <ul>\n                <li>Jeu de cartes de développement personnel</li>\n                <li>Format pratique et transportable</li>\n                <li>Illustrations inspirantes</li>\n                <li>Guide d'utilisation inclus</li>\n              </ul>\n            </div>\n            \n            <div class=\"product-price\">\n              <span class=\"price\">{{ product.price }}€</span>\n              <span class=\"stock-status\" [class.in-stock]=\"product.inStock\" [class.out-of-stock]=\"!product.inStock\">\n                {{ product.inStock ? 'En stock' : 'Rupture de stock' }}\n              </span>\n            </div>\n            \n            <div class=\"product-actions\">\n              <button \n                class=\"btn btn-primary btn-large\"\n                [disabled]=\"!product.inStock\"\n                (click)=\"addToCart()\"\n              >\n                {{ product.inStock ? 'Ajouter au panier' : 'Produit indisponible' }}\n              </button>\n              <button class=\"btn btn-outline\" routerLink=\"/products\">\n                Retour aux produits\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"product-tabs\">\n          <div class=\"tab-headers\">\n            <button \n              class=\"tab-header\"\n              [class.active]=\"activeTab === 'description'\"\n              (click)=\"activeTab = 'description'\"\n            >\n              Description détaillée\n            </button>\n            <button \n              class=\"tab-header\"\n              [class.active]=\"activeTab === 'usage'\"\n              (click)=\"activeTab = 'usage'\"\n            >\n              Mode d'emploi\n            </button>\n            <button \n              class=\"tab-header\"\n              [class.active]=\"activeTab === 'reviews'\"\n              (click)=\"activeTab = 'reviews'\"\n            >\n              Avis clients\n            </button>\n          </div>\n          \n          <div class=\"tab-content\">\n            <div *ngIf=\"activeTab === 'description'\" class=\"tab-panel\">\n              <h3>Description complète</h3>\n              <p>\n                Ce jeu de cartes de développement personnel a été conçu pour vous accompagner \n                dans votre parcours de croissance personnelle. Chaque carte contient des exercices \n                pratiques, des réflexions profondes et des conseils inspirants pour vous aider à \n                développer votre potentiel.\n              </p>\n              <p>\n                Que vous soyez débutant ou expérimenté dans le développement personnel, \n                ces cartes s'adaptent à votre rythme et à vos besoins spécifiques.\n              </p>\n            </div>\n            \n            <div *ngIf=\"activeTab === 'usage'\" class=\"tab-panel\">\n              <h3>Comment utiliser vos cartes</h3>\n              <ol>\n                <li>Choisissez un moment calme de votre journée</li>\n                <li>Mélangez les cartes et tirez-en une au hasard</li>\n                <li>Lisez attentivement le contenu de la carte</li>\n                <li>Prenez le temps de réfléchir à la question ou l'exercice proposé</li>\n                <li>Mettez en pratique les conseils dans votre quotidien</li>\n              </ol>\n            </div>\n            \n            <div *ngIf=\"activeTab === 'reviews'\" class=\"tab-panel\">\n              <h3>Avis de nos clients</h3>\n              <div class=\"review\">\n                <div class=\"review-rating\">★★★★★</div>\n                <p>\"Un outil formidable pour le développement personnel. Je recommande vivement !\"</p>\n                <cite>- Marie L.</cite>\n              </div>\n              <div class=\"review\">\n                <div class=\"review-rating\">★★★★☆</div>\n                <p>\"Très bien conçu, les exercices sont variés et inspirants.\"</p>\n                <cite>- Pierre D.</cite>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"loading\" *ngIf=\"!product && !error\">\n      <p>Chargement du produit...</p>\n    </div>\n    \n    <div class=\"error\" *ngIf=\"error\">\n      <div class=\"container\">\n        <h2>Produit non trouvé</h2>\n        <p>Le produit que vous recherchez n'existe pas ou n'est plus disponible.</p>\n        <button class=\"btn btn-primary\" routerLink=\"/products\">\n          Retour aux produits\n        </button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .product-detail {\n      padding: 100px 0 80px;\n      min-height: 100vh;\n    }\n    \n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n    \n    .breadcrumb {\n      margin-bottom: 30px;\n      color: #666;\n    }\n    \n    .breadcrumb a {\n      color: #3498db;\n      text-decoration: none;\n    }\n    \n    .breadcrumb span {\n      margin: 0 10px;\n    }\n    \n    .product-content {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 40px;\n      margin-bottom: 60px;\n    }\n    \n    @media (max-width: 768px) {\n      .product-content {\n        grid-template-columns: 1fr;\n      }\n    }\n    \n    .product-image img {\n      width: 100%;\n      border-radius: 10px;\n    }\n    \n    .product-info h1 {\n      color: #2c3e50;\n      margin-bottom: 10px;\n    }\n    \n    .product-category {\n      color: #3498db;\n      font-weight: 500;\n      margin-bottom: 20px;\n    }\n    \n    .product-description {\n      color: #666;\n      line-height: 1.6;\n      margin-bottom: 30px;\n    }\n    \n    .product-details {\n      margin-bottom: 30px;\n    }\n    \n    .product-details h3 {\n      color: #2c3e50;\n      margin-bottom: 15px;\n    }\n    \n    .product-details ul {\n      list-style: none;\n      padding: 0;\n    }\n    \n    .product-details li {\n      padding: 5px 0;\n      color: #666;\n    }\n    \n    .product-details li:before {\n      content: \"✓\";\n      color: #27ae60;\n      margin-right: 10px;\n    }\n    \n    .product-price {\n      display: flex;\n      align-items: center;\n      gap: 20px;\n      margin-bottom: 30px;\n    }\n    \n    .price {\n      font-size: 2rem;\n      font-weight: bold;\n      color: #e74c3c;\n    }\n    \n    .stock-status {\n      padding: 5px 15px;\n      border-radius: 20px;\n      font-size: 14px;\n      font-weight: 500;\n    }\n    \n    .in-stock {\n      background: #d5f4e6;\n      color: #27ae60;\n    }\n    \n    .out-of-stock {\n      background: #fadbd8;\n      color: #e74c3c;\n    }\n    \n    .product-actions {\n      display: flex;\n      gap: 15px;\n      flex-wrap: wrap;\n    }\n    \n    .btn {\n      padding: 12px 24px;\n      border: none;\n      border-radius: 5px;\n      cursor: pointer;\n      text-decoration: none;\n      display: inline-block;\n      text-align: center;\n      transition: all 0.3s ease;\n      font-size: 16px;\n    }\n    \n    .btn-large {\n      padding: 15px 30px;\n      font-size: 18px;\n    }\n    \n    .btn-primary {\n      background: #3498db;\n      color: white;\n    }\n    \n    .btn-primary:hover:not(:disabled) {\n      background: #2980b9;\n    }\n    \n    .btn-outline {\n      background: transparent;\n      color: #3498db;\n      border: 2px solid #3498db;\n    }\n    \n    .btn-outline:hover {\n      background: #3498db;\n      color: white;\n    }\n    \n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n    \n    .product-tabs {\n      background: white;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n      overflow: hidden;\n    }\n    \n    .tab-headers {\n      display: flex;\n      background: #f8f9fa;\n    }\n    \n    .tab-header {\n      flex: 1;\n      padding: 15px 20px;\n      border: none;\n      background: transparent;\n      cursor: pointer;\n      transition: all 0.3s ease;\n    }\n    \n    .tab-header.active {\n      background: white;\n      color: #3498db;\n      border-bottom: 2px solid #3498db;\n    }\n    \n    .tab-content {\n      padding: 30px;\n    }\n    \n    .review {\n      margin-bottom: 20px;\n      padding-bottom: 20px;\n      border-bottom: 1px solid #eee;\n    }\n    \n    .review-rating {\n      color: #f39c12;\n      margin-bottom: 10px;\n    }\n    \n    .review cite {\n      color: #666;\n      font-style: italic;\n    }\n    \n    .loading, .error {\n      text-align: center;\n      padding: 100px 0;\n    }\n    \n    .error h2 {\n      color: #e74c3c;\n      margin-bottom: 20px;\n    }\n  `]\n})\nexport class ProductDetailComponent implements OnInit {\n  product: Product | null = null;\n  error = false;\n  activeTab = 'description';\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private productService: ProductService\n  ) {}\n\n  ngOnInit(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.loadProduct(id);\n    } else {\n      this.error = true;\n    }\n  }\n\n  private loadProduct(id: string): void {\n    this.productService.getProduct(id).subscribe({\n      next: (product) => {\n        this.product = product;\n      },\n      error: () => {\n        this.error = true;\n      }\n    });\n  }\n\n  addToCart(): void {\n    if (this.product) {\n      // Logique d'ajout au panier à implémenter\n      alert(`${this.product.name} ajouté au panier !`);\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiCC,YAAY,QAAQ,iBAAiB;;;;;;;IAwFxDC,EADF,CAAAC,cAAA,cAA2D,SACrD;IAAAD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IACDD,EAAA,CAAAE,MAAA,yTAIF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,QAAG;IACDD,EAAA,CAAAE,MAAA,sLAEF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;;;;;IAGJH,EADF,CAAAC,cAAA,cAAqD,SAC/C;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAElCH,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAE,MAAA,uDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,yDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,4FAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,4DAAoD;IAE5DF,EAF4D,CAAAG,YAAA,EAAK,EAC1D,EACD;;;;;IAGJH,EADF,CAAAC,cAAA,cAAuD,SACjD;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE1BH,EADF,CAAAC,cAAA,cAAoB,cACS;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6FAA+E;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtFH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAClBF,EADkB,CAAAG,YAAA,EAAO,EACnB;IAEJH,EADF,CAAAC,cAAA,eAAoB,eACS;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,oFAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClEH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAErBF,EAFqB,CAAAG,YAAA,EAAO,EACpB,EACF;;;;;;IAhHRH,EAHN,CAAAC,cAAA,aAA4C,aACnB,aACG,WACJ;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;IAGJH,EADF,CAAAC,cAAA,cAA6B,cACA;IACzBD,EAAA,CAAAI,SAAA,eAAkD;IACpDJ,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG1DH,EADF,CAAAC,cAAA,eAA6B,UACvB;IAAAD,EAAA,CAAAE,MAAA,6BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEvBH,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAE,MAAA,qDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,wCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAElCF,EAFkC,CAAAG,YAAA,EAAK,EAChC,EACD;IAGJH,EADF,CAAAC,cAAA,eAA2B,gBACL;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,gBAAsG;IACpGD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAGJH,EADF,CAAAC,cAAA,eAA6B,kBAK1B;IADCD,EAAA,CAAAK,UAAA,mBAAAC,+DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAErBZ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAuD;IACrDD,EAAA,CAAAE,MAAA,6BACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA0B,eACC,kBAKtB;IADCD,EAAA,CAAAK,UAAA,mBAAAQ,+DAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAK,SAAA,GAAqB,aAAa;IAAA,EAAC;IAEnCd,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAU,+DAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAK,SAAA,GAAqB,OAAO;IAAA,EAAC;IAE7Bd,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAW,+DAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAK,SAAA,GAAqB,SAAS;IAAA,EAAC;IAE/Bd,EAAA,CAAAE,MAAA,sBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAC,cAAA,eAAyB;IA0BvBD,EAzBA,CAAAiB,UAAA,KAAAC,4CAAA,kBAA2D,KAAAC,4CAAA,mBAcN,KAAAC,4CAAA,mBAWE;IAgB/DpB,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IAhHMH,EAAA,CAAAqB,SAAA,IAAkB;IAAlBrB,EAAA,CAAAsB,iBAAA,CAAAb,MAAA,CAAAc,OAAA,CAAAC,IAAA,CAAkB;IAKjBxB,EAAA,CAAAqB,SAAA,GAAqB;IAACrB,EAAtB,CAAAyB,UAAA,QAAAhB,MAAA,CAAAc,OAAA,CAAAG,KAAA,EAAA1B,EAAA,CAAA2B,aAAA,CAAqB,QAAAlB,MAAA,CAAAc,OAAA,CAAAC,IAAA,CAAqB;IAI3CxB,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAsB,iBAAA,CAAAb,MAAA,CAAAc,OAAA,CAAAC,IAAA,CAAkB;IACMxB,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAb,MAAA,CAAAc,OAAA,CAAAK,QAAA,CAAsB;IACnB5B,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAsB,iBAAA,CAAAb,MAAA,CAAAc,OAAA,CAAAM,WAAA,CAAyB;IAalC7B,EAAA,CAAAqB,SAAA,IAAoB;IAApBrB,EAAA,CAAA8B,kBAAA,KAAArB,MAAA,CAAAc,OAAA,CAAAQ,KAAA,WAAoB;IACb/B,EAAA,CAAAqB,SAAA,EAAkC;IAACrB,EAAnC,CAAAgC,WAAA,aAAAvB,MAAA,CAAAc,OAAA,CAAAU,OAAA,CAAkC,kBAAAxB,MAAA,CAAAc,OAAA,CAAAU,OAAA,CAAwC;IACnGjC,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA8B,kBAAA,MAAArB,MAAA,CAAAc,OAAA,CAAAU,OAAA,wCACF;IAMEjC,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAAyB,UAAA,cAAAhB,MAAA,CAAAc,OAAA,CAAAU,OAAA,CAA6B;IAG7BjC,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA8B,kBAAA,MAAArB,MAAA,CAAAc,OAAA,CAAAU,OAAA,qDACF;IAYAjC,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAK,SAAA,mBAA4C;IAO5Cd,EAAA,CAAAqB,SAAA,GAAsC;IAAtCrB,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAK,SAAA,aAAsC;IAOtCd,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAAgC,WAAA,WAAAvB,MAAA,CAAAK,SAAA,eAAwC;IAQpCd,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAAK,SAAA,mBAAiC;IAcjCd,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAAK,SAAA,aAA2B;IAW3Bd,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAAyB,UAAA,SAAAhB,MAAA,CAAAK,SAAA,eAA6B;;;;;IAmBzCd,EADF,CAAAC,cAAA,cAAgD,QAC3C;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAC7BF,EAD6B,CAAAG,YAAA,EAAI,EAC3B;;;;;IAIFH,EAFJ,CAAAC,cAAA,cAAiC,aACR,SACjB;IAAAD,EAAA,CAAAE,MAAA,8BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,4EAAqE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5EH,EAAA,CAAAC,cAAA,iBAAuD;IACrDD,EAAA,CAAAE,MAAA,4BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;AAgOV,OAAM,MAAO+B,sBAAsB;EAKjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B;IAF9B,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IAPxB,KAAAf,OAAO,GAAmB,IAAI;IAC9B,KAAAgB,KAAK,GAAG,KAAK;IACb,KAAAzB,SAAS,GAAG,aAAa;EAMtB;EAEH0B,QAAQA,CAAA;IACN,MAAMC,EAAE,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAIH,EAAE,EAAE;MACN,IAAI,CAACI,WAAW,CAACJ,EAAE,CAAC;KACrB,MAAM;MACL,IAAI,CAACF,KAAK,GAAG,IAAI;;EAErB;EAEQM,WAAWA,CAACJ,EAAU;IAC5B,IAAI,CAACH,cAAc,CAACQ,UAAU,CAACL,EAAE,CAAC,CAACM,SAAS,CAAC;MAC3CC,IAAI,EAAGzB,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACxB,CAAC;MACDgB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACA,KAAK,GAAG,IAAI;MACnB;KACD,CAAC;EACJ;EAEA3B,SAASA,CAAA;IACP,IAAI,IAAI,CAACW,OAAO,EAAE;MAChB;MACA0B,KAAK,CAAC,GAAG,IAAI,CAAC1B,OAAO,CAACC,IAAI,qBAAqB,CAAC;;EAEpD;;;uBApCWU,sBAAsB,EAAAlC,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAArD,EAAA,CAAAkD,iBAAA,CAAAI,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAtBrB,sBAAsB;MAAAsB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1D,EAAA,CAAA2D,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxO/BjE,EA7HA,CAAAiB,UAAA,IAAAkD,qCAAA,mBAA4C,IAAAC,qCAAA,iBAyHI,IAAAC,qCAAA,iBAIf;;;UA7HJrE,EAAA,CAAAyB,UAAA,SAAAyC,GAAA,CAAA3C,OAAA,CAAa;UAyHpBvB,EAAA,CAAAqB,SAAA,EAAwB;UAAxBrB,EAAA,CAAAyB,UAAA,UAAAyC,GAAA,CAAA3C,OAAA,KAAA2C,GAAA,CAAA3B,KAAA,CAAwB;UAI1BvC,EAAA,CAAAqB,SAAA,EAAW;UAAXrB,EAAA,CAAAyB,UAAA,SAAAyC,GAAA,CAAA3B,KAAA,CAAW;;;qBA/HvBzC,YAAY,EAAAwE,EAAA,CAAAC,IAAA,EAAExE,YAAY,EAAAoD,EAAA,CAAAqB,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}