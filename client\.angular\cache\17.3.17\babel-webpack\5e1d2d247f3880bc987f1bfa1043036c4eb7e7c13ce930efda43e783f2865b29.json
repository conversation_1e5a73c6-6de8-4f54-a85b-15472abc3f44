{"ast": null, "code": "import { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class ServiceService {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.endpoint = '/services';\n  }\n  /**\n   * Récupère tous les services\n   */\n  getAllServices(params) {\n    return this.apiService.get(this.endpoint, params);\n  }\n  /**\n   * Récupère un service par son ID\n   */\n  getServiceById(id) {\n    return this.apiService.get(`${this.endpoint}/${id}`);\n  }\n  /**\n   * Récupère les services B2C (Particuliers)\n   */\n  getB2CServices() {\n    return this.apiService.get(`${this.endpoint}/b2c`);\n  }\n  /**\n   * Récupère les services B2B (Entreprises)\n   */\n  getB2BServices() {\n    return this.apiService.get(`${this.endpoint}/b2b`);\n  }\n  /**\n   * Récupère toutes les catégories de services\n   */\n  getCategories() {\n    return this.apiService.get(`${this.endpoint}/categories`);\n  }\n  /**\n   * Récupère les services groupés par type (B2C/B2B)\n   */\n  getServicesByType() {\n    return this.apiService.get(`${this.endpoint}/by-type`);\n  }\n  /**\n   * Recherche des services\n   */\n  searchServices(params) {\n    return this.apiService.get(`${this.endpoint}/search`, params);\n  }\n  /**\n   * Filtre les services par type\n   */\n  getServicesBySpecificType(type) {\n    return this.getAllServices({\n      type\n    });\n  }\n  /**\n   * Filtre les services par catégorie\n   */\n  getServicesByCategory(category) {\n    return this.getAllServices({\n      category\n    });\n  }\n  /**\n   * Alias pour getAllServices (pour compatibilité avec les composants)\n   */\n  getServices() {\n    return new Observable(observer => {\n      this.getAllServices().subscribe({\n        next: response => {\n          if (response.success && Array.isArray(response.data)) {\n            observer.next(response.data);\n          } else {\n            observer.next([]);\n          }\n        },\n        error: error => observer.error(error),\n        complete: () => observer.complete()\n      });\n    });\n  }\n  /**\n   * Récupère un service par son ID (alias pour getServiceById)\n   */\n  getService(id) {\n    return new Observable(observer => {\n      this.getServiceById(id).subscribe({\n        next: response => {\n          if (response.success && !Array.isArray(response.data) && typeof response.data === 'object' && 'id' in response.data) {\n            observer.next(response.data);\n          } else {\n            observer.error(new Error('Service not found'));\n          }\n        },\n        error: error => observer.error(error),\n        complete: () => observer.complete()\n      });\n    });\n  }\n  static {\n    this.ɵfac = function ServiceService_Factory(t) {\n      return new (t || ServiceService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ServiceService,\n      factory: ServiceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "ServiceService", "constructor", "apiService", "endpoint", "getAllServices", "params", "get", "getServiceById", "id", "getB2CServices", "getB2BServices", "getCategories", "getServicesByType", "searchServices", "getServicesBySpecificType", "type", "getServicesByCategory", "category", "getServices", "observer", "subscribe", "next", "response", "success", "Array", "isArray", "data", "error", "complete", "getService", "Error", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\services\\service.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { ApiService } from './api.service';\nimport { Service, ServiceResponse, ServiceSearchParams, ServicesByType } from '../models/service.interface';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ServiceService {\n  private readonly endpoint = '/services';\n\n  constructor(private apiService: ApiService) {}\n\n  /**\n   * Récupère tous les services\n   */\n  getAllServices(params?: { type?: 'B2C' | 'B2B'; category?: string }): Observable<ServiceResponse> {\n    return this.apiService.get<ServiceResponse>(this.endpoint, params);\n  }\n\n  /**\n   * Récupère un service par son ID\n   */\n  getServiceById(id: string): Observable<ServiceResponse> {\n    return this.apiService.get<ServiceResponse>(`${this.endpoint}/${id}`);\n  }\n\n  /**\n   * Récupère les services B2C (Particuliers)\n   */\n  getB2CServices(): Observable<ServiceResponse> {\n    return this.apiService.get<ServiceResponse>(`${this.endpoint}/b2c`);\n  }\n\n  /**\n   * Récupère les services B2B (Entreprises)\n   */\n  getB2BServices(): Observable<ServiceResponse> {\n    return this.apiService.get<ServiceResponse>(`${this.endpoint}/b2b`);\n  }\n\n  /**\n   * Récupère toutes les catégories de services\n   */\n  getCategories(): Observable<{ success: boolean; count: number; data: string[] }> {\n    return this.apiService.get<{ success: boolean; count: number; data: string[] }>(`${this.endpoint}/categories`);\n  }\n\n  /**\n   * Récupère les services groupés par type (B2C/B2B)\n   */\n  getServicesByType(): Observable<ServiceResponse> {\n    return this.apiService.get<ServiceResponse>(`${this.endpoint}/by-type`);\n  }\n\n  /**\n   * Recherche des services\n   */\n  searchServices(params: ServiceSearchParams): Observable<{\n    success: boolean;\n    query: string;\n    count: number;\n    data: Service[];\n  }> {\n    return this.apiService.get<{\n      success: boolean;\n      query: string;\n      count: number;\n      data: Service[];\n    }>(`${this.endpoint}/search`, params);\n  }\n\n  /**\n   * Filtre les services par type\n   */\n  getServicesBySpecificType(type: 'B2C' | 'B2B'): Observable<ServiceResponse> {\n    return this.getAllServices({ type });\n  }\n\n  /**\n   * Filtre les services par catégorie\n   */\n  getServicesByCategory(category: string): Observable<ServiceResponse> {\n    return this.getAllServices({ category });\n  }\n\n  /**\n   * Alias pour getAllServices (pour compatibilité avec les composants)\n   */\n  getServices(): Observable<Service[]> {\n    return new Observable(observer => {\n      this.getAllServices().subscribe({\n        next: (response) => {\n          if (response.success && Array.isArray(response.data)) {\n            observer.next(response.data);\n          } else {\n            observer.next([]);\n          }\n        },\n        error: (error) => observer.error(error),\n        complete: () => observer.complete()\n      });\n    });\n  }\n\n  /**\n   * Récupère un service par son ID (alias pour getServiceById)\n   */\n  getService(id: string): Observable<Service> {\n    return new Observable(observer => {\n      this.getServiceById(id).subscribe({\n        next: (response) => {\n          if (response.success && !Array.isArray(response.data) && typeof response.data === 'object' && 'id' in response.data) {\n            observer.next(response.data as Service);\n          } else {\n            observer.error(new Error('Service not found'));\n          }\n        },\n        error: (error) => observer.error(error),\n        complete: () => observer.complete()\n      });\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,MAAM;;;AAOjC,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAFb,KAAAC,QAAQ,GAAG,WAAW;EAEM;EAE7C;;;EAGAC,cAAcA,CAACC,MAAoD;IACjE,OAAO,IAAI,CAACH,UAAU,CAACI,GAAG,CAAkB,IAAI,CAACH,QAAQ,EAAEE,MAAM,CAAC;EACpE;EAEA;;;EAGAE,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACN,UAAU,CAACI,GAAG,CAAkB,GAAG,IAAI,CAACH,QAAQ,IAAIK,EAAE,EAAE,CAAC;EACvE;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACP,UAAU,CAACI,GAAG,CAAkB,GAAG,IAAI,CAACH,QAAQ,MAAM,CAAC;EACrE;EAEA;;;EAGAO,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACR,UAAU,CAACI,GAAG,CAAkB,GAAG,IAAI,CAACH,QAAQ,MAAM,CAAC;EACrE;EAEA;;;EAGAQ,aAAaA,CAAA;IACX,OAAO,IAAI,CAACT,UAAU,CAACI,GAAG,CAAsD,GAAG,IAAI,CAACH,QAAQ,aAAa,CAAC;EAChH;EAEA;;;EAGAS,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACV,UAAU,CAACI,GAAG,CAAkB,GAAG,IAAI,CAACH,QAAQ,UAAU,CAAC;EACzE;EAEA;;;EAGAU,cAAcA,CAACR,MAA2B;IAMxC,OAAO,IAAI,CAACH,UAAU,CAACI,GAAG,CAKvB,GAAG,IAAI,CAACH,QAAQ,SAAS,EAAEE,MAAM,CAAC;EACvC;EAEA;;;EAGAS,yBAAyBA,CAACC,IAAmB;IAC3C,OAAO,IAAI,CAACX,cAAc,CAAC;MAAEW;IAAI,CAAE,CAAC;EACtC;EAEA;;;EAGAC,qBAAqBA,CAACC,QAAgB;IACpC,OAAO,IAAI,CAACb,cAAc,CAAC;MAAEa;IAAQ,CAAE,CAAC;EAC1C;EAEA;;;EAGAC,WAAWA,CAAA;IACT,OAAO,IAAInB,UAAU,CAACoB,QAAQ,IAAG;MAC/B,IAAI,CAACf,cAAc,EAAE,CAACgB,SAAS,CAAC;QAC9BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAACI,IAAI,CAAC,EAAE;YACpDP,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAACI,IAAI,CAAC;WAC7B,MAAM;YACLP,QAAQ,CAACE,IAAI,CAAC,EAAE,CAAC;;QAErB,CAAC;QACDM,KAAK,EAAGA,KAAK,IAAKR,QAAQ,CAACQ,KAAK,CAACA,KAAK,CAAC;QACvCC,QAAQ,EAAEA,CAAA,KAAMT,QAAQ,CAACS,QAAQ;OAClC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;EAGAC,UAAUA,CAACrB,EAAU;IACnB,OAAO,IAAIT,UAAU,CAACoB,QAAQ,IAAG;MAC/B,IAAI,CAACZ,cAAc,CAACC,EAAE,CAAC,CAACY,SAAS,CAAC;QAChCC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAACI,IAAI,CAAC,IAAI,OAAOJ,QAAQ,CAACI,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,EAAE;YACnHP,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAACI,IAAe,CAAC;WACxC,MAAM;YACLP,QAAQ,CAACQ,KAAK,CAAC,IAAIG,KAAK,CAAC,mBAAmB,CAAC,CAAC;;QAElD,CAAC;QACDH,KAAK,EAAGA,KAAK,IAAKR,QAAQ,CAACQ,KAAK,CAACA,KAAK,CAAC;QACvCC,QAAQ,EAAEA,CAAA,KAAMT,QAAQ,CAACS,QAAQ;OAClC,CAAC;IACJ,CAAC,CAAC;EACJ;;;uBAlHW5B,cAAc,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdlC,cAAc;MAAAmC,OAAA,EAAdnC,cAAc,CAAAoC,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}