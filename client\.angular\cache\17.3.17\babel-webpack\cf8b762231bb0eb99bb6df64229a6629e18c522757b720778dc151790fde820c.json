{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nfunction AboutComponent_li_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 37);\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const specialty_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", specialty_r1, \" \");\n  }\n}\nfunction AboutComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"h3\");\n    i0.ɵɵtext(2, \"Pr\\u00EAt \\u00E0 commencer votre transformation ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"D\\u00E9couvrez nos cartes et services pour d\\u00E9buter votre parcours de reconnexion.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"a\", 41);\n    i0.ɵɵelement(7, \"i\", 42);\n    i0.ɵɵtext(8, \" Voir nos cartes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 43);\n    i0.ɵɵelement(10, \"i\", 44);\n    i0.ɵɵtext(11, \" Nous contacter \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class AboutComponent {\n  constructor() {\n    this.isHomePage = false;\n    this.founderInfo = {\n      name: 'Sarah Martinez',\n      title: 'Coach de vie diplômée en psychologie',\n      experience: '8+ années d\\'expérience',\n      specialties: ['Accompagnement personnel et professionnel', 'Gestion du stress et de l\\'anxiété', 'Développement de la confiance en soi', 'Équilibre vie professionnelle/personnelle'],\n      mission: 'Accompagner chaque personne vers une vie plus équilibrée et épanouie en se reconnectant avec son essence authentique.',\n      image: '/assets/images/founder.jpg'\n    };\n    this.brandStory = {\n      problem: 'Dans notre société hyperconnectée, nous perdons souvent le contact avec nous-mêmes et nos proches.',\n      solution: 'Disconnect To Connect propose une approche innovante : se déconnecter du numérique pour se reconnecter à l\\'essentiel.',\n      validation: 'Cette méthode est validée par la recherche en psychologie positive et les neurosciences.',\n      impact: 'Plus de 500 personnes ont déjà transformé leur vie grâce à nos outils et accompagnements.'\n    };\n  }\n  ngOnInit() {\n    // Animations d'entrée\n  }\n  static {\n    this.ɵfac = function AboutComponent_Factory(t) {\n      return new (t || AboutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AboutComponent,\n      selectors: [[\"app-about\"]],\n      inputs: {\n        isHomePage: \"isHomePage\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 100,\n      vars: 12,\n      consts: [[\"id\", \"about\", 1, \"about\", \"section\"], [1, \"container\"], [1, \"founder-section\"], [1, \"row\"], [1, \"col-6\"], [1, \"founder-image\"], [1, \"founder-photo\", 3, \"src\", \"alt\"], [1, \"image-decoration\"], [1, \"founder-content\"], [1, \"section-title\"], [1, \"founder-name\"], [1, \"founder-title\"], [1, \"founder-experience\"], [1, \"founder-specialties\"], [1, \"specialties-list\"], [\"class\", \"specialty-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"founder-mission\"], [1, \"fas\", \"fa-quote-left\", \"quote-icon\"], [1, \"brand-story-section\"], [1, \"section-header\", \"text-center\"], [1, \"section-subtitle\"], [1, \"story-timeline\"], [1, \"timeline-item\"], [1, \"timeline-icon\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"timeline-content\"], [1, \"fas\", \"fa-lightbulb\"], [1, \"fas\", \"fa-microscope\"], [1, \"fas\", \"fa-heart\"], [1, \"values-section\"], [1, \"values-grid\"], [1, \"value-card\"], [1, \"value-icon\"], [1, \"fas\", \"fa-users\"], [1, \"fas\", \"fa-seedling\"], [1, \"fas\", \"fa-balance-scale\"], [\"class\", \"cta-section text-center\", 4, \"ngIf\"], [1, \"specialty-item\"], [1, \"fas\", \"fa-check-circle\"], [1, \"cta-section\", \"text-center\"], [1, \"cta-buttons\"], [\"routerLink\", \"/products\", 1, \"btn\", \"btn-primary\", \"btn-lg\"], [1, \"fas\", \"fa-cards-blank\"], [\"routerLink\", \"/contact\", 1, \"btn\", \"btn-outline\", \"btn-lg\"], [1, \"fas\", \"fa-envelope\"]],\n      template: function AboutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"img\", 6)(7, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 4)(9, \"div\", 8)(10, \"h2\", 9);\n          i0.ɵɵtext(11, \"\\u00C0 propos de la fondatrice\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"h3\", 10);\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\", 11);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 12);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"h4\");\n          i0.ɵɵtext(20, \"Domaines d'expertise :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"ul\", 14);\n          i0.ɵɵtemplate(22, AboutComponent_li_22_Template, 3, 1, \"li\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"blockquote\", 16);\n          i0.ɵɵelement(24, \"i\", 17);\n          i0.ɵɵelementStart(25, \"p\");\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"div\", 19)(29, \"h2\", 9);\n          i0.ɵɵtext(30, \"L'histoire de Disconnect To Connect\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"p\", 20);\n          i0.ɵɵtext(32, \" Une approche r\\u00E9volutionnaire pour retrouver l'\\u00E9quilibre dans un monde hyperconnect\\u00E9 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 21)(34, \"div\", 22)(35, \"div\", 23);\n          i0.ɵɵelement(36, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 25)(38, \"h3\");\n          i0.ɵɵtext(39, \"Le Probl\\u00E8me\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\");\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 22)(43, \"div\", 23);\n          i0.ɵɵelement(44, \"i\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 25)(46, \"h3\");\n          i0.ɵɵtext(47, \"La Solution\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"p\");\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 22)(51, \"div\", 23);\n          i0.ɵɵelement(52, \"i\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 25)(54, \"h3\");\n          i0.ɵɵtext(55, \"La Validation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"div\", 22)(59, \"div\", 23);\n          i0.ɵɵelement(60, \"i\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 25)(62, \"h3\");\n          i0.ɵɵtext(63, \"L'Impact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"p\");\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(66, \"div\", 29)(67, \"div\", 19)(68, \"h2\", 9);\n          i0.ɵɵtext(69, \"Nos Valeurs\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 30)(71, \"div\", 31)(72, \"div\", 32);\n          i0.ɵɵelement(73, \"i\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"h3\");\n          i0.ɵɵtext(75, \"Authenticit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"p\");\n          i0.ɵɵtext(77, \"Nous croyons en l'importance d'\\u00EAtre vrai avec soi-m\\u00EAme et les autres.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(78, \"div\", 31)(79, \"div\", 32);\n          i0.ɵɵelement(80, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"h3\");\n          i0.ɵɵtext(82, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"p\");\n          i0.ɵɵtext(84, \"Favoriser les liens authentiques entre les personnes et avec soi-m\\u00EAme.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 31)(86, \"div\", 32);\n          i0.ɵɵelement(87, \"i\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"h3\");\n          i0.ɵɵtext(89, \"Croissance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"p\");\n          i0.ɵɵtext(91, \"Accompagner chacun dans son d\\u00E9veloppement personnel et professionnel.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"div\", 31)(93, \"div\", 32);\n          i0.ɵɵelement(94, \"i\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"h3\");\n          i0.ɵɵtext(96, \"\\u00C9quilibre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"p\");\n          i0.ɵɵtext(98, \"Trouver l'harmonie entre vie num\\u00E9rique et vie r\\u00E9elle.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(99, AboutComponent_div_99_Template, 12, 0, \"div\", 36);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", ctx.founderInfo.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx.founderInfo.name);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.founderInfo.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.founderInfo.title);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.founderInfo.experience);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.founderInfo.specialties);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.founderInfo.mission);\n          i0.ɵɵadvance(15);\n          i0.ɵɵtextInterpolate(ctx.brandStory.problem);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.brandStory.solution);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.brandStory.validation);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.brandStory.impact);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngIf\", ctx.isHomePage);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink],\n      styles: [\".about[_ngcontent-%COMP%] {\\n  background: var(--white);\\n}\\n\\n.founder-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-xxl);\\n}\\n\\n.founder-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n}\\n.founder-image[_ngcontent-%COMP%]   .founder-photo[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  height: auto;\\n  border-radius: var(--border-radius-xl);\\n  box-shadow: 0 20px 60px rgba(63, 25, 0, 0.15);\\n  position: relative;\\n  z-index: 2;\\n}\\n.founder-image[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -20px;\\n  left: -20px;\\n  right: 20px;\\n  bottom: 20px;\\n  background: var(--gradient-accent);\\n  border-radius: var(--border-radius-xl);\\n  z-index: 1;\\n}\\n\\n.founder-content[_ngcontent-%COMP%] {\\n  padding-left: var(--spacing-lg);\\n}\\n.founder-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  margin-bottom: var(--spacing-md);\\n}\\n.founder-content[_ngcontent-%COMP%]   .founder-name[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: var(--text-dark);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.founder-content[_ngcontent-%COMP%]   .founder-title[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  color: var(--secondary-color);\\n  font-weight: 600;\\n  margin-bottom: var(--spacing-xs);\\n}\\n.founder-content[_ngcontent-%COMP%]   .founder-experience[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n  margin-bottom: var(--spacing-lg);\\n}\\n\\n.founder-specialties[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-lg);\\n}\\n.founder-specialties[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: var(--text-dark);\\n  margin-bottom: var(--spacing-sm);\\n  font-size: 1.125rem;\\n}\\n\\n.specialties-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n}\\n.specialties-list[_ngcontent-%COMP%]   .specialty-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: var(--spacing-xs);\\n  color: var(--text-light);\\n}\\n.specialties-list[_ngcontent-%COMP%]   .specialty-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--secondary-color);\\n  margin-right: var(--spacing-sm);\\n  font-size: 1.1rem;\\n}\\n\\n.founder-mission[_ngcontent-%COMP%] {\\n  background: var(--accent-color);\\n  padding: var(--spacing-lg);\\n  border-radius: var(--border-radius-lg);\\n  border-left: 4px solid var(--secondary-color);\\n  position: relative;\\n  margin: 0;\\n}\\n.founder-mission[_ngcontent-%COMP%]   .quote-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: var(--spacing-sm);\\n  left: var(--spacing-sm);\\n  color: var(--secondary-color);\\n  font-size: 1.5rem;\\n  opacity: 0.7;\\n}\\n.founder-mission[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: var(--spacing-lg);\\n  font-style: italic;\\n  color: var(--text-dark);\\n  line-height: 1.6;\\n}\\n\\n.brand-story-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-xxl);\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-xl);\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  margin-bottom: var(--spacing-sm);\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  color: var(--text-light);\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.story-timeline[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: var(--spacing-lg);\\n  margin-top: var(--spacing-xl);\\n}\\n\\n.timeline-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: var(--spacing-lg);\\n  background: var(--white);\\n  border-radius: var(--border-radius-lg);\\n  box-shadow: 0 4px 20px rgba(63, 25, 0, 0.08);\\n  transition: var(--transition-normal);\\n}\\n.timeline-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 30px rgba(63, 25, 0, 0.15);\\n}\\n\\n.timeline-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  background: var(--gradient-primary);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto var(--spacing-md);\\n}\\n.timeline-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--white);\\n  font-size: 1.5rem;\\n}\\n\\n.timeline-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--text-dark);\\n  margin-bottom: var(--spacing-sm);\\n  font-size: 1.25rem;\\n}\\n.timeline-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n  line-height: 1.6;\\n}\\n\\n.values-section[_ngcontent-%COMP%] {\\n  background: var(--accent-color);\\n  padding: var(--spacing-xxl) 0;\\n  margin: 0 -50vw;\\n  margin-left: calc(-50vw + 50%);\\n  margin-right: calc(-50vw + 50%);\\n}\\n\\n.values-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: var(--spacing-lg);\\n  margin-top: var(--spacing-xl);\\n}\\n\\n.value-card[_ngcontent-%COMP%] {\\n  background: var(--white);\\n  padding: var(--spacing-xl);\\n  border-radius: var(--border-radius-lg);\\n  text-align: center;\\n  box-shadow: 0 4px 20px rgba(63, 25, 0, 0.08);\\n  transition: var(--transition-normal);\\n}\\n.value-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 30px rgba(63, 25, 0, 0.15);\\n}\\n\\n.value-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  background: var(--gradient-accent);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto var(--spacing-md);\\n}\\n.value-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 2rem;\\n}\\n\\n.value-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--text-dark);\\n  margin-bottom: var(--spacing-sm);\\n  font-size: 1.25rem;\\n}\\n\\n.value-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n  line-height: 1.6;\\n}\\n\\n.cta-section[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-xxl);\\n  padding: var(--spacing-xl);\\n  background: var(--gradient-accent);\\n  border-radius: var(--border-radius-xl);\\n}\\n.cta-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--text-dark);\\n  margin-bottom: var(--spacing-sm);\\n  font-size: 1.75rem;\\n}\\n.cta-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n  margin-bottom: var(--spacing-lg);\\n  font-size: 1.125rem;\\n}\\n\\n.cta-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  justify-content: center;\\n  flex-wrap: wrap;\\n}\\n.cta-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n}\\n.cta-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .founder-content[_ngcontent-%COMP%] {\\n    padding-left: 0;\\n    margin-top: var(--spacing-lg);\\n  }\\n  .story-timeline[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .values-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .cta-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  .cta-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 300px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .founder-name[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .timeline-item[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md);\\n  }\\n  .value-card[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg);\\n  }\\n  .value-icon[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .value-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "specialty_r1", "AboutComponent", "constructor", "isHomePage", "founderInfo", "name", "title", "experience", "specialties", "mission", "image", "brandStory", "problem", "solution", "validation", "impact", "ngOnInit", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AboutComponent_Template", "rf", "ctx", "ɵɵtemplate", "AboutComponent_li_22_Template", "AboutComponent_div_99_Template", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\about\\about.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\about\\about.component.html"], "sourcesContent": ["import { Component, OnInit, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'app-about',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './about.component.html',\n  styleUrls: ['./about.component.scss']\n})\nexport class AboutComponent implements OnInit {\n  @Input() isHomePage: boolean = false;\n\n  founderInfo = {\n    name: '<PERSON>',\n    title: 'Coach de vie diplômée en psychologie',\n    experience: '8+ années d\\'expérience',\n    specialties: [\n      'Accompagnement personnel et professionnel',\n      'Gestion du stress et de l\\'anxiété',\n      'Développement de la confiance en soi',\n      'Équilibre vie professionnelle/personnelle'\n    ],\n    mission: 'Accompagner chaque personne vers une vie plus équilibrée et épanouie en se reconnectant avec son essence authentique.',\n    image: '/assets/images/founder.jpg'\n  };\n\n  brandStory = {\n    problem: 'Dans notre société hyperconnectée, nous perdons souvent le contact avec nous-mêmes et nos proches.',\n    solution: 'Disconnect To Connect propose une approche innovante : se déconnecter du numérique pour se reconnecter à l\\'essentiel.',\n    validation: 'Cette méthode est validée par la recherche en psychologie positive et les neurosciences.',\n    impact: 'Plus de 500 personnes ont déjà transformé leur vie grâce à nos outils et accompagnements.'\n  };\n\n  ngOnInit(): void {\n    // Animations d'entrée\n  }\n}\n", "<section class=\"about section\" id=\"about\">\n  <div class=\"container\">\n    <!-- Section Fondatrice -->\n    <div class=\"founder-section\">\n      <div class=\"row\">\n        <div class=\"col-6\">\n          <div class=\"founder-image\">\n            <img [src]=\"founderInfo.image\" [alt]=\"founderInfo.name\" class=\"founder-photo\">\n            <div class=\"image-decoration\"></div>\n          </div>\n        </div>\n        <div class=\"col-6\">\n          <div class=\"founder-content\">\n            <h2 class=\"section-title\">À propos de la fondatrice</h2>\n            <h3 class=\"founder-name\">{{ founderInfo.name }}</h3>\n            <p class=\"founder-title\">{{ founderInfo.title }}</p>\n            <p class=\"founder-experience\">{{ founderInfo.experience }}</p>\n            \n            <div class=\"founder-specialties\">\n              <h4>Domaines d'expertise :</h4>\n              <ul class=\"specialties-list\">\n                <li *ngFor=\"let specialty of founderInfo.specialties\" class=\"specialty-item\">\n                  <i class=\"fas fa-check-circle\"></i>\n                  {{ specialty }}\n                </li>\n              </ul>\n            </div>\n            \n            <blockquote class=\"founder-mission\">\n              <i class=\"fas fa-quote-left quote-icon\"></i>\n              <p>{{ founderInfo.mission }}</p>\n            </blockquote>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Section Histoire de la marque -->\n    <div class=\"brand-story-section\">\n      <div class=\"section-header text-center\">\n        <h2 class=\"section-title\">L'histoire de Disconnect To Connect</h2>\n        <p class=\"section-subtitle\">\n          Une approche révolutionnaire pour retrouver l'équilibre dans un monde hyperconnecté\n        </p>\n      </div>\n\n      <div class=\"story-timeline\">\n        <div class=\"timeline-item\">\n          <div class=\"timeline-icon\">\n            <i class=\"fas fa-exclamation-triangle\"></i>\n          </div>\n          <div class=\"timeline-content\">\n            <h3>Le Problème</h3>\n            <p>{{ brandStory.problem }}</p>\n          </div>\n        </div>\n\n        <div class=\"timeline-item\">\n          <div class=\"timeline-icon\">\n            <i class=\"fas fa-lightbulb\"></i>\n          </div>\n          <div class=\"timeline-content\">\n            <h3>La Solution</h3>\n            <p>{{ brandStory.solution }}</p>\n          </div>\n        </div>\n\n        <div class=\"timeline-item\">\n          <div class=\"timeline-icon\">\n            <i class=\"fas fa-microscope\"></i>\n          </div>\n          <div class=\"timeline-content\">\n            <h3>La Validation</h3>\n            <p>{{ brandStory.validation }}</p>\n          </div>\n        </div>\n\n        <div class=\"timeline-item\">\n          <div class=\"timeline-icon\">\n            <i class=\"fas fa-heart\"></i>\n          </div>\n          <div class=\"timeline-content\">\n            <h3>L'Impact</h3>\n            <p>{{ brandStory.impact }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Section Valeurs -->\n    <div class=\"values-section\">\n      <div class=\"section-header text-center\">\n        <h2 class=\"section-title\">Nos Valeurs</h2>\n      </div>\n\n      <div class=\"values-grid\">\n        <div class=\"value-card\">\n          <div class=\"value-icon\">\n            <i class=\"fas fa-heart\"></i>\n          </div>\n          <h3>Authenticité</h3>\n          <p>Nous croyons en l'importance d'être vrai avec soi-même et les autres.</p>\n        </div>\n\n        <div class=\"value-card\">\n          <div class=\"value-icon\">\n            <i class=\"fas fa-users\"></i>\n          </div>\n          <h3>Connexion</h3>\n          <p>Favoriser les liens authentiques entre les personnes et avec soi-même.</p>\n        </div>\n\n        <div class=\"value-card\">\n          <div class=\"value-icon\">\n            <i class=\"fas fa-seedling\"></i>\n          </div>\n          <h3>Croissance</h3>\n          <p>Accompagner chacun dans son développement personnel et professionnel.</p>\n        </div>\n\n        <div class=\"value-card\">\n          <div class=\"value-icon\">\n            <i class=\"fas fa-balance-scale\"></i>\n          </div>\n          <h3>Équilibre</h3>\n          <p>Trouver l'harmonie entre vie numérique et vie réelle.</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- CTA Section -->\n    <div class=\"cta-section text-center\" *ngIf=\"isHomePage\">\n      <h3>Prêt à commencer votre transformation ?</h3>\n      <p>Découvrez nos cartes et services pour débuter votre parcours de reconnexion.</p>\n      <div class=\"cta-buttons\">\n        <a routerLink=\"/products\" class=\"btn btn-primary btn-lg\">\n          <i class=\"fas fa-cards-blank\"></i>\n          Voir nos cartes\n        </a>\n        <a routerLink=\"/contact\" class=\"btn btn-outline btn-lg\">\n          <i class=\"fas fa-envelope\"></i>\n          Nous contacter\n        </a>\n      </div>\n    </div>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;ICmB9BC,EAAA,CAAAC,cAAA,aAA6E;IAC3ED,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IADHJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,YAAA,MACF;;;;;IA4GVP,EADF,CAAAC,cAAA,cAAwD,SAClD;IAAAD,EAAA,CAAAG,MAAA,wDAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,6FAA4E;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEjFJ,EADF,CAAAC,cAAA,cAAyB,YACkC;IACvDD,EAAA,CAAAE,SAAA,YAAkC;IAClCF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,YAAwD;IACtDD,EAAA,CAAAE,SAAA,aAA+B;IAC/BF,EAAA,CAAAG,MAAA,wBACF;IAEJH,EAFI,CAAAI,YAAA,EAAI,EACA,EACF;;;ADrIV,OAAM,MAAOI,cAAc;EAP3BC,YAAA;IAQW,KAAAC,UAAU,GAAY,KAAK;IAEpC,KAAAC,WAAW,GAAG;MACZC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,sCAAsC;MAC7CC,UAAU,EAAE,yBAAyB;MACrCC,WAAW,EAAE,CACX,2CAA2C,EAC3C,oCAAoC,EACpC,sCAAsC,EACtC,2CAA2C,CAC5C;MACDC,OAAO,EAAE,uHAAuH;MAChIC,KAAK,EAAE;KACR;IAED,KAAAC,UAAU,GAAG;MACXC,OAAO,EAAE,oGAAoG;MAC7GC,QAAQ,EAAE,wHAAwH;MAClIC,UAAU,EAAE,0FAA0F;MACtGC,MAAM,EAAE;KACT;;EAEDC,QAAQA,CAAA;IACN;EAAA;;;uBAzBSf,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAgB,SAAA;MAAAC,MAAA;QAAAf,UAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GAAA3B,EAAA,CAAA4B,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLjBlC,EANV,CAAAC,cAAA,iBAA0C,aACjB,aAEQ,aACV,aACI,aACU;UAEzBD,EADA,CAAAE,SAAA,aAA8E,aAC1C;UAExCF,EADE,CAAAI,YAAA,EAAM,EACF;UAGFJ,EAFJ,CAAAC,cAAA,aAAmB,aACY,aACD;UAAAD,EAAA,CAAAG,MAAA,sCAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxDJ,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAG,MAAA,IAAsB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpDJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAG,MAAA,IAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpDJ,EAAA,CAAAC,cAAA,aAA8B;UAAAD,EAAA,CAAAG,MAAA,IAA4B;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG5DJ,EADF,CAAAC,cAAA,eAAiC,UAC3B;UAAAD,EAAA,CAAAG,MAAA,8BAAsB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/BJ,EAAA,CAAAC,cAAA,cAA6B;UAC3BD,EAAA,CAAAoC,UAAA,KAAAC,6BAAA,iBAA6E;UAKjFrC,EADE,CAAAI,YAAA,EAAK,EACD;UAENJ,EAAA,CAAAC,cAAA,sBAAoC;UAClCD,EAAA,CAAAE,SAAA,aAA4C;UAC5CF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,IAAyB;UAKtCH,EALsC,CAAAI,YAAA,EAAI,EACrB,EACT,EACF,EACF,EACF;UAKFJ,EAFJ,CAAAC,cAAA,eAAiC,eACS,aACZ;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,aAA4B;UAC1BD,EAAA,CAAAG,MAAA,4GACF;UACFH,EADE,CAAAI,YAAA,EAAI,EACA;UAIFJ,EAFJ,CAAAC,cAAA,eAA4B,eACC,eACE;UACzBD,EAAA,CAAAE,SAAA,aAA2C;UAC7CF,EAAA,CAAAI,YAAA,EAAM;UAEJJ,EADF,CAAAC,cAAA,eAA8B,UACxB;UAAAD,EAAA,CAAAG,MAAA,wBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,IAAwB;UAE/BH,EAF+B,CAAAI,YAAA,EAAI,EAC3B,EACF;UAGJJ,EADF,CAAAC,cAAA,eAA2B,eACE;UACzBD,EAAA,CAAAE,SAAA,aAAgC;UAClCF,EAAA,CAAAI,YAAA,EAAM;UAEJJ,EADF,CAAAC,cAAA,eAA8B,UACxB;UAAAD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,IAAyB;UAEhCH,EAFgC,CAAAI,YAAA,EAAI,EAC5B,EACF;UAGJJ,EADF,CAAAC,cAAA,eAA2B,eACE;UACzBD,EAAA,CAAAE,SAAA,aAAiC;UACnCF,EAAA,CAAAI,YAAA,EAAM;UAEJJ,EADF,CAAAC,cAAA,eAA8B,UACxB;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,IAA2B;UAElCH,EAFkC,CAAAI,YAAA,EAAI,EAC9B,EACF;UAGJJ,EADF,CAAAC,cAAA,eAA2B,eACE;UACzBD,EAAA,CAAAE,SAAA,aAA4B;UAC9BF,EAAA,CAAAI,YAAA,EAAM;UAEJJ,EADF,CAAAC,cAAA,eAA8B,UACxB;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,IAAuB;UAIlCH,EAJkC,CAAAI,YAAA,EAAI,EAC1B,EACF,EACF,EACF;UAKFJ,EAFJ,CAAAC,cAAA,eAA4B,eACc,aACZ;UAAAD,EAAA,CAAAG,MAAA,mBAAW;UACvCH,EADuC,CAAAI,YAAA,EAAK,EACtC;UAIFJ,EAFJ,CAAAC,cAAA,eAAyB,eACC,eACE;UACtBD,EAAA,CAAAE,SAAA,aAA4B;UAC9BF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,yBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,uFAAqE;UAC1EH,EAD0E,CAAAI,YAAA,EAAI,EACxE;UAGJJ,EADF,CAAAC,cAAA,eAAwB,eACE;UACtBD,EAAA,CAAAE,SAAA,aAA4B;UAC9BF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,mFAAsE;UAC3EH,EAD2E,CAAAI,YAAA,EAAI,EACzE;UAGJJ,EADF,CAAAC,cAAA,eAAwB,eACE;UACtBD,EAAA,CAAAE,SAAA,aAA+B;UACjCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kFAAqE;UAC1EH,EAD0E,CAAAI,YAAA,EAAI,EACxE;UAGJJ,EADF,CAAAC,cAAA,eAAwB,eACE;UACtBD,EAAA,CAAAE,SAAA,aAAoC;UACtCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,sBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,uEAAqD;UAG9DH,EAH8D,CAAAI,YAAA,EAAI,EACxD,EACF,EACF;UAGNJ,EAAA,CAAAoC,UAAA,KAAAE,8BAAA,mBAAwD;UAe5DtC,EADE,CAAAI,YAAA,EAAM,EACE;;;UA3IOJ,EAAA,CAAAK,SAAA,GAAyB;UAACL,EAA1B,CAAAuC,UAAA,QAAAJ,GAAA,CAAAxB,WAAA,CAAAM,KAAA,EAAAjB,EAAA,CAAAwC,aAAA,CAAyB,QAAAL,GAAA,CAAAxB,WAAA,CAAAC,IAAA,CAAyB;UAO9BZ,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAyC,iBAAA,CAAAN,GAAA,CAAAxB,WAAA,CAAAC,IAAA,CAAsB;UACtBZ,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAyC,iBAAA,CAAAN,GAAA,CAAAxB,WAAA,CAAAE,KAAA,CAAuB;UAClBb,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAyC,iBAAA,CAAAN,GAAA,CAAAxB,WAAA,CAAAG,UAAA,CAA4B;UAK5Bd,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAuC,UAAA,YAAAJ,GAAA,CAAAxB,WAAA,CAAAI,WAAA,CAA0B;UASnDf,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAyC,iBAAA,CAAAN,GAAA,CAAAxB,WAAA,CAAAK,OAAA,CAAyB;UAuB3BhB,EAAA,CAAAK,SAAA,IAAwB;UAAxBL,EAAA,CAAAyC,iBAAA,CAAAN,GAAA,CAAAjB,UAAA,CAAAC,OAAA,CAAwB;UAUxBnB,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAyC,iBAAA,CAAAN,GAAA,CAAAjB,UAAA,CAAAE,QAAA,CAAyB;UAUzBpB,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAyC,iBAAA,CAAAN,GAAA,CAAAjB,UAAA,CAAAG,UAAA,CAA2B;UAU3BrB,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAyC,iBAAA,CAAAN,GAAA,CAAAjB,UAAA,CAAAI,MAAA,CAAuB;UAgDItB,EAAA,CAAAK,SAAA,IAAgB;UAAhBL,EAAA,CAAAuC,UAAA,SAAAJ,GAAA,CAAAzB,UAAA,CAAgB;;;qBD5H9CZ,YAAY,EAAA4C,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7C,YAAY,EAAA8C,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}