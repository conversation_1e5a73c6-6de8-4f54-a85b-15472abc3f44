{"ast": null, "code": "import { map } from './map';\nexport function mapTo(value) {\n  return map(() => value);\n}", "map": {"version": 3, "names": ["map", "mapTo", "value"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Site e-commerce/client/node_modules/rxjs/dist/esm/internal/operators/mapTo.js"], "sourcesContent": ["import { map } from './map';\nexport function mapTo(value) {\n    return map(() => value);\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAC3B,OAAO,SAASC,KAAKA,CAACC,KAAK,EAAE;EACzB,OAAOF,GAAG,CAAC,MAAME,KAAK,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}