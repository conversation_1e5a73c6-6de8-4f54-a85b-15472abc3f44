{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/service.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = a0 => [\"/services\", a0];\nfunction ServiceDetailComponent_div_0_li_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const benefit_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(benefit_r2);\n  }\n}\nfunction ServiceDetailComponent_div_0_div_63_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 10);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 29);\n    i0.ɵɵtext(10, \" En savoir plus \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const relatedService_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(relatedService_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(relatedService_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(relatedService_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", relatedService_r4.price, \"\\u20AC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(6, _c0, relatedService_r4.id));\n  }\n}\nfunction ServiceDetailComponent_div_0_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"h2\");\n    i0.ɵɵtext(2, \"Services similaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtemplate(4, ServiceDetailComponent_div_0_div_63_div_4_Template, 11, 8, \"div\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.relatedServices);\n  }\n}\nfunction ServiceDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"nav\", 5)(3, \"a\", 6);\n    i0.ɵɵtext(4, \"Accueil\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 7);\n    i0.ɵɵtext(8, \"Services\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"div\", 10);\n    i0.ɵɵelement(16, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 11)(18, \"h1\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 12);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 13);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 14)(25, \"div\", 15)(26, \"h3\");\n    i0.ɵɵtext(27, \"Dur\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 15)(31, \"h3\");\n    i0.ɵɵtext(32, \"Prix\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 16);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 15)(36, \"h3\");\n    i0.ɵɵtext(37, \"Format\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 17)(41, \"h2\");\n    i0.ɵɵtext(42, \"Description compl\\u00E8te\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"h3\");\n    i0.ɵɵtext(46, \"Ce que vous allez apprendre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"ul\");\n    i0.ɵɵtemplate(48, ServiceDetailComponent_div_0_li_48_Template, 2, 1, \"li\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"h3\");\n    i0.ɵɵtext(50, \"Pour qui ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\");\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 19)(54, \"h2\");\n    i0.ɵɵtext(55, \"R\\u00E9server ce service\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"p\");\n    i0.ɵɵtext(57, \"Pr\\u00EAt \\u00E0 commencer votre parcours de d\\u00E9veloppement personnel ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 20)(59, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ServiceDetailComponent_div_0_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.bookService());\n    });\n    i0.ɵɵtext(60, \" R\\u00E9server maintenant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"button\", 22);\n    i0.ɵɵtext(62, \" Poser une question \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(63, ServiceDetailComponent_div_0_div_63_Template, 5, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r2.service.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.service.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.service.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.service.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.service.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.service.duration);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.service.price, \"\\u20AC\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getServiceFormat());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getFullDescription(), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getServiceBenefits());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.getTargetAudience());\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.relatedServices.length > 0);\n  }\n}\nfunction ServiceDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"p\");\n    i0.ɵɵtext(2, \"Chargement du service...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ServiceDetailComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 4)(2, \"h2\");\n    i0.ɵɵtext(3, \"Service non trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Le service que vous recherchez n'existe pas ou n'est plus disponible.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 32);\n    i0.ɵɵtext(7, \" Retour aux services \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class ServiceDetailComponent {\n  constructor(route, router, serviceService) {\n    this.route = route;\n    this.router = router;\n    this.serviceService = serviceService;\n    this.service = null;\n    this.relatedServices = [];\n    this.error = false;\n  }\n  ngOnInit() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.loadService(id);\n      this.loadRelatedServices(id);\n    } else {\n      this.error = true;\n    }\n  }\n  loadService(id) {\n    this.serviceService.getService(id).subscribe({\n      next: service => {\n        this.service = service;\n      },\n      error: () => {\n        this.error = true;\n      }\n    });\n  }\n  loadRelatedServices(currentId) {\n    this.serviceService.getServices().subscribe({\n      next: services => {\n        this.relatedServices = services.filter(s => s.id !== currentId).slice(0, 3);\n      },\n      error: () => {\n        // Ignore l'erreur pour les services liés\n      }\n    });\n  }\n  getServiceFormat() {\n    if (!this.service) return '';\n    switch (this.service.category) {\n      case 'Coaching':\n        return 'Séance individuelle en présentiel ou visio';\n      case 'Atelier':\n        return 'Atelier de groupe en présentiel';\n      case 'Formation':\n        return 'Formation en ligne avec support';\n      default:\n        return 'Format à définir';\n    }\n  }\n  getFullDescription() {\n    if (!this.service) return '';\n    return `Ce service de ${this.service.category.toLowerCase()} est conçu pour vous accompagner \n    dans votre développement personnel. Avec une approche personnalisée et des outils pratiques, \n    vous développerez les compétences nécessaires pour atteindre vos objectifs et améliorer \n    votre bien-être au quotidien.`;\n  }\n  getServiceBenefits() {\n    if (!this.service) return [];\n    const commonBenefits = ['Développement de la confiance en soi', 'Amélioration de la gestion du stress', 'Clarification de vos objectifs personnels', 'Techniques pratiques applicables au quotidien'];\n    switch (this.service.category) {\n      case 'Coaching':\n        return [...commonBenefits, 'Accompagnement personnalisé', 'Plan d\\'action sur mesure'];\n      case 'Atelier':\n        return [...commonBenefits, 'Échanges enrichissants avec le groupe', 'Exercices pratiques en équipe'];\n      case 'Formation':\n        return [...commonBenefits, 'Contenu structuré et progressif', 'Accès illimité aux ressources'];\n      default:\n        return commonBenefits;\n    }\n  }\n  getTargetAudience() {\n    if (!this.service) return '';\n    return `Ce service s'adresse à toute personne souhaitant améliorer son développement personnel, \n    que vous soyez débutant ou que vous ayez déjà une expérience dans ce domaine. \n    Aucun prérequis particulier n'est nécessaire.`;\n  }\n  bookService() {\n    if (this.service) {\n      // Logique de réservation à implémenter\n      alert(`Réservation pour ${this.service.name} - Vous allez être redirigé vers le formulaire de contact.`);\n      this.router.navigate(['/contact']);\n    }\n  }\n  static {\n    this.ɵfac = function ServiceDetailComponent_Factory(t) {\n      return new (t || ServiceDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ServiceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ServiceDetailComponent,\n      selectors: [[\"app-service-detail\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"service-detail\", 4, \"ngIf\"], [\"class\", \"loading\", 4, \"ngIf\"], [\"class\", \"error\", 4, \"ngIf\"], [1, \"service-detail\"], [1, \"container\"], [1, \"breadcrumb\"], [\"routerLink\", \"/\"], [\"routerLink\", \"/services\"], [1, \"service-content\"], [1, \"service-header\"], [1, \"service-icon\"], [1, \"service-info\"], [1, \"service-category\"], [1, \"service-description\"], [1, \"service-details\"], [1, \"detail-card\"], [1, \"price\"], [1, \"service-description-full\"], [4, \"ngFor\", \"ngForOf\"], [1, \"service-booking\"], [1, \"booking-actions\"], [1, \"btn\", \"btn-primary\", \"btn-large\", 3, \"click\"], [\"routerLink\", \"/contact\", 1, \"btn\", \"btn-outline\"], [\"class\", \"related-services\", 4, \"ngIf\"], [1, \"related-services\"], [1, \"services-grid\"], [\"class\", \"service-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"service-card\"], [1, \"service-price\"], [1, \"btn\", \"btn-primary\", 3, \"routerLink\"], [1, \"loading\"], [1, \"error\"], [\"routerLink\", \"/services\", 1, \"btn\", \"btn-primary\"]],\n      template: function ServiceDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ServiceDetailComponent_div_0_Template, 64, 13, \"div\", 0)(1, ServiceDetailComponent_div_1_Template, 3, 0, \"div\", 1)(2, ServiceDetailComponent_div_2_Template, 8, 0, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.service);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.service && !ctx.error);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, RouterModule, i1.RouterLink],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.service-detail[_ngcontent-%COMP%] {\\n  padding: 100px 0 80px;\\n  min-height: 100vh;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  color: #666;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #3498db;\\n  text-decoration: none;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin: 0 10px;\\n}\\n\\n.service-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 30px;\\n  margin-bottom: 40px;\\n  padding: 30px;\\n  background: white;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.service-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #3498db;\\n}\\n\\n.service-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.service-category[_ngcontent-%COMP%] {\\n  color: #3498db;\\n  font-weight: 500;\\n  margin-bottom: 15px;\\n}\\n\\n.service-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.6;\\n}\\n\\n.service-details[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 40px;\\n}\\n\\n.detail-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 20px;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  text-align: center;\\n}\\n\\n.detail-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.detail-card[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  color: #e74c3c;\\n}\\n\\n.service-description-full[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 40px;\\n}\\n\\n.service-description-full[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .service-description-full[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 15px;\\n}\\n\\n.service-description-full[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.6;\\n  margin-bottom: 20px;\\n}\\n\\n.service-description-full[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n}\\n\\n.service-description-full[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 5px 0;\\n  color: #666;\\n}\\n\\n.service-description-full[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before {\\n  content: \\\"\\u2713\\\";\\n  color: #27ae60;\\n  margin-right: 10px;\\n}\\n\\n.service-booking[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 30px;\\n  border-radius: 10px;\\n  text-align: center;\\n  margin-bottom: 40px;\\n}\\n\\n.service-booking[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 15px;\\n}\\n\\n.service-booking[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 25px;\\n}\\n\\n.booking-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  text-decoration: none;\\n  display: inline-block;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  font-size: 16px;\\n}\\n\\n.btn-large[_ngcontent-%COMP%] {\\n  padding: 15px 30px;\\n  font-size: 18px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #2980b9;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #3498db;\\n  border: 2px solid #3498db;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%]:hover {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.related-services[_ngcontent-%COMP%] {\\n  margin-top: 60px;\\n}\\n\\n.related-services[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #2c3e50;\\n  margin-bottom: 30px;\\n}\\n\\n.services-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n\\n.service-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 20px;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  text-align: center;\\n}\\n\\n.service-card[_ngcontent-%COMP%]   .service-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #3498db;\\n  margin-bottom: 15px;\\n}\\n\\n.service-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.service-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 15px;\\n}\\n\\n.service-card[_ngcontent-%COMP%]   .service-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: bold;\\n  color: #e74c3c;\\n  margin-bottom: 15px;\\n}\\n\\n.loading[_ngcontent-%COMP%], .error[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 100px 0;\\n}\\n\\n.error[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  margin-bottom: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uLy4uLy4uL1NpdGUlMjBlLWNvbW1lcmNlL2NsaWVudC9zcmMvYXBwL2NvbXBvbmVudHMvc2VydmljZS1kZXRhaWwvc2VydmljZS1kZXRhaWwuY29tcG9uZW50LnRzIiwid2VicGFjazovLy4vc3JjL2FwcC9jb21wb25lbnRzL3NlcnZpY2UtZGV0YWlsL3NlcnZpY2UtZGV0YWlsLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUNDWjtFQUNFLHFCQUFBO0VBQ0EsaUJBQUE7QURDTjs7QUNFSTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7QURDTjs7QUNFSTtFQUNFLG1CQUFBO0VBQ0EsV0FBQTtBRENOOztBQ0VJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FEQ047O0FDRUk7RUFDRSxjQUFBO0FEQ047O0FDRUk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLHlDQUFBO0FEQ047O0FDRUk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtBRENOOztBQ0VJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0FEQ047O0FDRUk7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBRENOOztBQ0VJO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0FEQ047O0FDRUk7RUFDRSxhQUFBO0VBQ0EsMkRBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QURDTjs7QUNFSTtFQUNFLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUNBQUE7RUFDQSxrQkFBQTtBRENOOztBQ0VJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0FEQ047O0FDRUk7RUFDRSxpQkFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtBRENOOztBQ0VJO0VBQ0UsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx5Q0FBQTtFQUNBLG1CQUFBO0FEQ047O0FDRUk7O0VBRUUsY0FBQTtFQUNBLG1CQUFBO0FEQ047O0FDRUk7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBRENOOztBQ0VJO0VBQ0UsZ0JBQUE7RUFDQSxVQUFBO0FEQ047O0FDRUk7RUFDRSxjQUFBO0VBQ0EsV0FBQTtBRENOOztBQ0VJO0VBQ0UsWUFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBRENOOztBQ0VJO0VBQ0UsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0FEQ047O0FDRUk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7QURDTjs7QUNFSTtFQUNFLFdBQUE7RUFDQSxtQkFBQTtBRENOOztBQ0VJO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7QURDTjs7QUNFSTtFQUNFLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLHFCQUFBO0VBQ0EscUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtBRENOOztBQ0VJO0VBQ0Usa0JBQUE7RUFDQSxlQUFBO0FEQ047O0FDRUk7RUFDRSxtQkFBQTtFQUNBLFlBQUE7QURDTjs7QUNFSTtFQUNFLG1CQUFBO0FEQ047O0FDRUk7RUFDRSx1QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBRENOOztBQ0VJO0VBQ0UsbUJBQUE7RUFDQSxZQUFBO0FEQ047O0FDRUk7RUFDRSxnQkFBQTtBRENOOztBQ0VJO0VBQ0Usa0JBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7QURDTjs7QUNFSTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLFNBQUE7QURDTjs7QUNFSTtFQUNFLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUNBQUE7RUFDQSxrQkFBQTtBRENOOztBQ0VJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBRENOOztBQ0VJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0FEQ047O0FDRUk7RUFDRSxXQUFBO0VBQ0EsbUJBQUE7QURDTjs7QUNFSTtFQUNFLGlCQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7QURDTjs7QUNFSTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7QURDTjs7QUNFSTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtBRENOIiwic291cmNlc0NvbnRlbnQiOlsiQGNoYXJzZXQgXCJVVEYtOFwiO1xuLnNlcnZpY2UtZGV0YWlsIHtcbiAgcGFkZGluZzogMTAwcHggMCA4MHB4O1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbn1cblxuLmNvbnRhaW5lciB7XG4gIG1heC13aWR0aDogMTIwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbiAgcGFkZGluZzogMCAyMHB4O1xufVxuXG4uYnJlYWRjcnVtYiB7XG4gIG1hcmdpbi1ib3R0b206IDMwcHg7XG4gIGNvbG9yOiAjNjY2O1xufVxuXG4uYnJlYWRjcnVtYiBhIHtcbiAgY29sb3I6ICMzNDk4ZGI7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn1cblxuLmJyZWFkY3J1bWIgc3BhbiB7XG4gIG1hcmdpbjogMCAxMHB4O1xufVxuXG4uc2VydmljZS1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDMwcHg7XG4gIG1hcmdpbi1ib3R0b206IDQwcHg7XG4gIHBhZGRpbmc6IDMwcHg7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICBib3gtc2hhZG93OiAwIDVweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLnNlcnZpY2UtaWNvbiBpIHtcbiAgZm9udC1zaXplOiA0cmVtO1xuICBjb2xvcjogIzM0OThkYjtcbn1cblxuLnNlcnZpY2UtaW5mbyBoMSB7XG4gIGNvbG9yOiAjMmMzZTUwO1xuICBtYXJnaW4tYm90dG9tOiAxMHB4O1xufVxuXG4uc2VydmljZS1jYXRlZ29yeSB7XG4gIGNvbG9yOiAjMzQ5OGRiO1xuICBmb250LXdlaWdodDogNTAwO1xuICBtYXJnaW4tYm90dG9tOiAxNXB4O1xufVxuXG4uc2VydmljZS1kZXNjcmlwdGlvbiB7XG4gIGNvbG9yOiAjNjY2O1xuICBsaW5lLWhlaWdodDogMS42O1xufVxuXG4uc2VydmljZS1kZXRhaWxzIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyMDBweCwgMWZyKSk7XG4gIGdhcDogMjBweDtcbiAgbWFyZ2luLWJvdHRvbTogNDBweDtcbn1cblxuLmRldGFpbC1jYXJkIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5kZXRhaWwtY2FyZCBoMyB7XG4gIGNvbG9yOiAjMmMzZTUwO1xuICBtYXJnaW4tYm90dG9tOiAxMHB4O1xufVxuXG4uZGV0YWlsLWNhcmQgLnByaWNlIHtcbiAgZm9udC1zaXplOiAxLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xuICBjb2xvcjogI2U3NGMzYztcbn1cblxuLnNlcnZpY2UtZGVzY3JpcHRpb24tZnVsbCB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBwYWRkaW5nOiAzMHB4O1xuICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICBib3gtc2hhZG93OiAwIDVweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgbWFyZ2luLWJvdHRvbTogNDBweDtcbn1cblxuLnNlcnZpY2UtZGVzY3JpcHRpb24tZnVsbCBoMixcbi5zZXJ2aWNlLWRlc2NyaXB0aW9uLWZ1bGwgaDMge1xuICBjb2xvcjogIzJjM2U1MDtcbiAgbWFyZ2luLWJvdHRvbTogMTVweDtcbn1cblxuLnNlcnZpY2UtZGVzY3JpcHRpb24tZnVsbCBwIHtcbiAgY29sb3I6ICM2NjY7XG4gIGxpbmUtaGVpZ2h0OiAxLjY7XG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XG59XG5cbi5zZXJ2aWNlLWRlc2NyaXB0aW9uLWZ1bGwgdWwge1xuICBsaXN0LXN0eWxlOiBub25lO1xuICBwYWRkaW5nOiAwO1xufVxuXG4uc2VydmljZS1kZXNjcmlwdGlvbi1mdWxsIGxpIHtcbiAgcGFkZGluZzogNXB4IDA7XG4gIGNvbG9yOiAjNjY2O1xufVxuXG4uc2VydmljZS1kZXNjcmlwdGlvbi1mdWxsIGxpOmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiw6LCnMKTXCI7XG4gIGNvbG9yOiAjMjdhZTYwO1xuICBtYXJnaW4tcmlnaHQ6IDEwcHg7XG59XG5cbi5zZXJ2aWNlLWJvb2tpbmcge1xuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICBwYWRkaW5nOiAzMHB4O1xuICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDQwcHg7XG59XG5cbi5zZXJ2aWNlLWJvb2tpbmcgaDIge1xuICBjb2xvcjogIzJjM2U1MDtcbiAgbWFyZ2luLWJvdHRvbTogMTVweDtcbn1cblxuLnNlcnZpY2UtYm9va2luZyBwIHtcbiAgY29sb3I6ICM2NjY7XG4gIG1hcmdpbi1ib3R0b206IDI1cHg7XG59XG5cbi5ib29raW5nLWFjdGlvbnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDE1cHg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBmbGV4LXdyYXA6IHdyYXA7XG59XG5cbi5idG4ge1xuICBwYWRkaW5nOiAxMnB4IDI0cHg7XG4gIGJvcmRlcjogbm9uZTtcbiAgYm9yZGVyLXJhZGl1czogNXB4O1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGZvbnQtc2l6ZTogMTZweDtcbn1cblxuLmJ0bi1sYXJnZSB7XG4gIHBhZGRpbmc6IDE1cHggMzBweDtcbiAgZm9udC1zaXplOiAxOHB4O1xufVxuXG4uYnRuLXByaW1hcnkge1xuICBiYWNrZ3JvdW5kOiAjMzQ5OGRiO1xuICBjb2xvcjogd2hpdGU7XG59XG5cbi5idG4tcHJpbWFyeTpob3ZlciB7XG4gIGJhY2tncm91bmQ6ICMyOTgwYjk7XG59XG5cbi5idG4tb3V0bGluZSB7XG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICBjb2xvcjogIzM0OThkYjtcbiAgYm9yZGVyOiAycHggc29saWQgIzM0OThkYjtcbn1cblxuLmJ0bi1vdXRsaW5lOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogIzM0OThkYjtcbiAgY29sb3I6IHdoaXRlO1xufVxuXG4ucmVsYXRlZC1zZXJ2aWNlcyB7XG4gIG1hcmdpbi10b3A6IDYwcHg7XG59XG5cbi5yZWxhdGVkLXNlcnZpY2VzIGgyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBjb2xvcjogIzJjM2U1MDtcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcbn1cblxuLnNlcnZpY2VzLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDMwMHB4LCAxZnIpKTtcbiAgZ2FwOiAyMHB4O1xufVxuXG4uc2VydmljZS1jYXJkIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5zZXJ2aWNlLWNhcmQgLnNlcnZpY2UtaWNvbiBpIHtcbiAgZm9udC1zaXplOiAycmVtO1xuICBjb2xvcjogIzM0OThkYjtcbiAgbWFyZ2luLWJvdHRvbTogMTVweDtcbn1cblxuLnNlcnZpY2UtY2FyZCBoMyB7XG4gIGNvbG9yOiAjMmMzZTUwO1xuICBtYXJnaW4tYm90dG9tOiAxMHB4O1xufVxuXG4uc2VydmljZS1jYXJkIHAge1xuICBjb2xvcjogIzY2NjtcbiAgbWFyZ2luLWJvdHRvbTogMTVweDtcbn1cblxuLnNlcnZpY2UtY2FyZCAuc2VydmljZS1wcmljZSB7XG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuICBmb250LXdlaWdodDogYm9sZDtcbiAgY29sb3I6ICNlNzRjM2M7XG4gIG1hcmdpbi1ib3R0b206IDE1cHg7XG59XG5cbi5sb2FkaW5nLCAuZXJyb3Ige1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDEwMHB4IDA7XG59XG5cbi5lcnJvciBoMiB7XG4gIGNvbG9yOiAjZTc0YzNjO1xuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xufSIsIlxuICAgIC5zZXJ2aWNlLWRldGFpbCB7XG4gICAgICBwYWRkaW5nOiAxMDBweCAwIDgwcHg7XG4gICAgICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgICB9XG4gICAgXG4gICAgLmNvbnRhaW5lciB7XG4gICAgICBtYXgtd2lkdGg6IDEyMDBweDtcbiAgICAgIG1hcmdpbjogMCBhdXRvO1xuICAgICAgcGFkZGluZzogMCAyMHB4O1xuICAgIH1cbiAgICBcbiAgICAuYnJlYWRjcnVtYiB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgfVxuICAgIFxuICAgIC5icmVhZGNydW1iIGEge1xuICAgICAgY29sb3I6ICMzNDk4ZGI7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgfVxuICAgIFxuICAgIC5icmVhZGNydW1iIHNwYW4ge1xuICAgICAgbWFyZ2luOiAwIDEwcHg7XG4gICAgfVxuICAgIFxuICAgIC5zZXJ2aWNlLWhlYWRlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMzBweDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDQwcHg7XG4gICAgICBwYWRkaW5nOiAzMHB4O1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgICAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDAsMCwwLDAuMSk7XG4gICAgfVxuICAgIFxuICAgIC5zZXJ2aWNlLWljb24gaSB7XG4gICAgICBmb250LXNpemU6IDRyZW07XG4gICAgICBjb2xvcjogIzM0OThkYjtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtaW5mbyBoMSB7XG4gICAgICBjb2xvcjogIzJjM2U1MDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XG4gICAgfVxuICAgIFxuICAgIC5zZXJ2aWNlLWNhdGVnb3J5IHtcbiAgICAgIGNvbG9yOiAjMzQ5OGRiO1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7XG4gICAgfVxuICAgIFxuICAgIC5zZXJ2aWNlLWRlc2NyaXB0aW9uIHtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtZGV0YWlscyB7XG4gICAgICBkaXNwbGF5OiBncmlkO1xuICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyMDBweCwgMWZyKSk7XG4gICAgICBnYXA6IDIwcHg7XG4gICAgICBtYXJnaW4tYm90dG9tOiA0MHB4O1xuICAgIH1cbiAgICBcbiAgICAuZGV0YWlsLWNhcmQge1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgICAgIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgwLDAsMCwwLjEpO1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIH1cbiAgICBcbiAgICAuZGV0YWlsLWNhcmQgaDMge1xuICAgICAgY29sb3I6ICMyYzNlNTA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICAgIH1cbiAgICBcbiAgICAuZGV0YWlsLWNhcmQgLnByaWNlIHtcbiAgICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICBjb2xvcjogI2U3NGMzYztcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtZGVzY3JpcHRpb24tZnVsbCB7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICAgIHBhZGRpbmc6IDMwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgICAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDAsMCwwLDAuMSk7XG4gICAgICBtYXJnaW4tYm90dG9tOiA0MHB4O1xuICAgIH1cbiAgICBcbiAgICAuc2VydmljZS1kZXNjcmlwdGlvbi1mdWxsIGgyLFxuICAgIC5zZXJ2aWNlLWRlc2NyaXB0aW9uLWZ1bGwgaDMge1xuICAgICAgY29sb3I6ICMyYzNlNTA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxNXB4O1xuICAgIH1cbiAgICBcbiAgICAuc2VydmljZS1kZXNjcmlwdGlvbi1mdWxsIHAge1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICBsaW5lLWhlaWdodDogMS42O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtZGVzY3JpcHRpb24tZnVsbCB1bCB7XG4gICAgICBsaXN0LXN0eWxlOiBub25lO1xuICAgICAgcGFkZGluZzogMDtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtZGVzY3JpcHRpb24tZnVsbCBsaSB7XG4gICAgICBwYWRkaW5nOiA1cHggMDtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgIH1cbiAgICBcbiAgICAuc2VydmljZS1kZXNjcmlwdGlvbi1mdWxsIGxpOmJlZm9yZSB7XG4gICAgICBjb250ZW50OiBcIsOiwpzCk1wiO1xuICAgICAgY29sb3I6ICMyN2FlNjA7XG4gICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7XG4gICAgfVxuICAgIFxuICAgIC5zZXJ2aWNlLWJvb2tpbmcge1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgIHBhZGRpbmc6IDMwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogNDBweDtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtYm9va2luZyBoMiB7XG4gICAgICBjb2xvcjogIzJjM2U1MDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7XG4gICAgfVxuICAgIFxuICAgIC5zZXJ2aWNlLWJvb2tpbmcgcCB7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDI1cHg7XG4gICAgfVxuICAgIFxuICAgIC5ib29raW5nLWFjdGlvbnMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGdhcDogMTVweDtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgZmxleC13cmFwOiB3cmFwO1xuICAgIH1cbiAgICBcbiAgICAuYnRuIHtcbiAgICAgIHBhZGRpbmc6IDEycHggMjRweDtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDVweDtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgfVxuICAgIFxuICAgIC5idG4tbGFyZ2Uge1xuICAgICAgcGFkZGluZzogMTVweCAzMHB4O1xuICAgICAgZm9udC1zaXplOiAxOHB4O1xuICAgIH1cbiAgICBcbiAgICAuYnRuLXByaW1hcnkge1xuICAgICAgYmFja2dyb3VuZDogIzM0OThkYjtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICB9XG4gICAgXG4gICAgLmJ0bi1wcmltYXJ5OmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICMyOTgwYjk7XG4gICAgfVxuICAgIFxuICAgIC5idG4tb3V0bGluZSB7XG4gICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgICAgIGNvbG9yOiAjMzQ5OGRiO1xuICAgICAgYm9yZGVyOiAycHggc29saWQgIzM0OThkYjtcbiAgICB9XG4gICAgXG4gICAgLmJ0bi1vdXRsaW5lOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICMzNDk4ZGI7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgfVxuICAgIFxuICAgIC5yZWxhdGVkLXNlcnZpY2VzIHtcbiAgICAgIG1hcmdpbi10b3A6IDYwcHg7XG4gICAgfVxuICAgIFxuICAgIC5yZWxhdGVkLXNlcnZpY2VzIGgyIHtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIGNvbG9yOiAjMmMzZTUwO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2VzLWdyaWQge1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMzAwcHgsIDFmcikpO1xuICAgICAgZ2FwOiAyMHB4O1xuICAgIH1cbiAgICBcbiAgICAuc2VydmljZS1jYXJkIHtcbiAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgICAgcGFkZGluZzogMjBweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gICAgICBib3gtc2hhZG93OiAwIDVweCAxNXB4IHJnYmEoMCwwLDAsMC4xKTtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtY2FyZCAuc2VydmljZS1pY29uIGkge1xuICAgICAgZm9udC1zaXplOiAycmVtO1xuICAgICAgY29sb3I6ICMzNDk4ZGI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxNXB4O1xuICAgIH1cbiAgICBcbiAgICAuc2VydmljZS1jYXJkIGgzIHtcbiAgICAgIGNvbG9yOiAjMmMzZTUwO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtY2FyZCBwIHtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDtcbiAgICB9XG4gICAgXG4gICAgLnNlcnZpY2UtY2FyZCAuc2VydmljZS1wcmljZSB7XG4gICAgICBmb250LXNpemU6IDEuMnJlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xuICAgICAgY29sb3I6ICNlNzRjM2M7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxNXB4O1xuICAgIH1cbiAgICBcbiAgICAubG9hZGluZywgLmVycm9yIHtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIHBhZGRpbmc6IDEwMHB4IDA7XG4gICAgfVxuICAgIFxuICAgIC5lcnJvciBoMiB7XG4gICAgICBjb2xvcjogI2U3NGMzYztcbiAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "benefit_r2", "ɵɵelement", "ɵɵclassMap", "relatedService_r4", "icon", "name", "description", "ɵɵtextInterpolate1", "price", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "id", "ɵɵtemplate", "ServiceDetailComponent_div_0_div_63_div_4_Template", "ctx_r2", "relatedServices", "ServiceDetailComponent_div_0_li_48_Template", "ɵɵlistener", "ServiceDetailComponent_div_0_Template_button_click_59_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "bookService", "ServiceDetailComponent_div_0_div_63_Template", "service", "category", "duration", "getServiceFormat", "getFullDescription", "getServiceBenefits", "getTargetAudience", "length", "ServiceDetailComponent", "constructor", "route", "router", "serviceService", "error", "ngOnInit", "snapshot", "paramMap", "get", "loadService", "loadRelatedServices", "getService", "subscribe", "next", "currentId", "getServices", "services", "filter", "s", "slice", "toLowerCase", "commonBenefits", "alert", "navigate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ServiceService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ServiceDetailComponent_Template", "rf", "ctx", "ServiceDetailComponent_div_0_Template", "ServiceDetailComponent_div_1_Template", "ServiceDetailComponent_div_2_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\service-detail\\service-detail.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { ServiceService } from '../../services/service.service';\nimport { Service } from '../../models/service.interface';\n\n@Component({\n  selector: 'app-service-detail',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <div class=\"service-detail\" *ngIf=\"service\">\n      <div class=\"container\">\n        <nav class=\"breadcrumb\">\n          <a routerLink=\"/\">Accueil</a>\n          <span>/</span>\n          <a routerLink=\"/services\">Services</a>\n          <span>/</span>\n          <span>{{ service.name }}</span>\n        </nav>\n        \n        <div class=\"service-content\">\n          <div class=\"service-header\">\n            <div class=\"service-icon\">\n              <i [class]=\"service.icon\"></i>\n            </div>\n            <div class=\"service-info\">\n              <h1>{{ service.name }}</h1>\n              <p class=\"service-category\">{{ service.category }}</p>\n              <p class=\"service-description\">{{ service.description }}</p>\n            </div>\n          </div>\n          \n          <div class=\"service-details\">\n            <div class=\"detail-card\">\n              <h3>Durée</h3>\n              <p>{{ service.duration }}</p>\n            </div>\n            <div class=\"detail-card\">\n              <h3>Prix</h3>\n              <p class=\"price\">{{ service.price }}€</p>\n            </div>\n            <div class=\"detail-card\">\n              <h3>Format</h3>\n              <p>{{ getServiceFormat() }}</p>\n            </div>\n          </div>\n          \n          <div class=\"service-description-full\">\n            <h2>Description complète</h2>\n            <p>\n              {{ getFullDescription() }}\n            </p>\n            \n            <h3>Ce que vous allez apprendre</h3>\n            <ul>\n              <li *ngFor=\"let benefit of getServiceBenefits()\">{{ benefit }}</li>\n            </ul>\n            \n            <h3>Pour qui ?</h3>\n            <p>{{ getTargetAudience() }}</p>\n          </div>\n          \n          <div class=\"service-booking\">\n            <h2>Réserver ce service</h2>\n            <p>Prêt à commencer votre parcours de développement personnel ?</p>\n            <div class=\"booking-actions\">\n              <button class=\"btn btn-primary btn-large\" (click)=\"bookService()\">\n                Réserver maintenant\n              </button>\n              <button class=\"btn btn-outline\" routerLink=\"/contact\">\n                Poser une question\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"related-services\" *ngIf=\"relatedServices.length > 0\">\n          <h2>Services similaires</h2>\n          <div class=\"services-grid\">\n            <div class=\"service-card\" *ngFor=\"let relatedService of relatedServices\">\n              <div class=\"service-icon\">\n                <i [class]=\"relatedService.icon\"></i>\n              </div>\n              <h3>{{ relatedService.name }}</h3>\n              <p>{{ relatedService.description }}</p>\n              <div class=\"service-price\">{{ relatedService.price }}€</div>\n              <button \n                class=\"btn btn-primary\"\n                [routerLink]=\"['/services', relatedService.id]\"\n              >\n                En savoir plus\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"loading\" *ngIf=\"!service && !error\">\n      <p>Chargement du service...</p>\n    </div>\n    \n    <div class=\"error\" *ngIf=\"error\">\n      <div class=\"container\">\n        <h2>Service non trouvé</h2>\n        <p>Le service que vous recherchez n'existe pas ou n'est plus disponible.</p>\n        <button class=\"btn btn-primary\" routerLink=\"/services\">\n          Retour aux services\n        </button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .service-detail {\n      padding: 100px 0 80px;\n      min-height: 100vh;\n    }\n    \n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n    \n    .breadcrumb {\n      margin-bottom: 30px;\n      color: #666;\n    }\n    \n    .breadcrumb a {\n      color: #3498db;\n      text-decoration: none;\n    }\n    \n    .breadcrumb span {\n      margin: 0 10px;\n    }\n    \n    .service-header {\n      display: flex;\n      align-items: center;\n      gap: 30px;\n      margin-bottom: 40px;\n      padding: 30px;\n      background: white;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n    }\n    \n    .service-icon i {\n      font-size: 4rem;\n      color: #3498db;\n    }\n    \n    .service-info h1 {\n      color: #2c3e50;\n      margin-bottom: 10px;\n    }\n    \n    .service-category {\n      color: #3498db;\n      font-weight: 500;\n      margin-bottom: 15px;\n    }\n    \n    .service-description {\n      color: #666;\n      line-height: 1.6;\n    }\n    \n    .service-details {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-bottom: 40px;\n    }\n    \n    .detail-card {\n      background: white;\n      padding: 20px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n      text-align: center;\n    }\n    \n    .detail-card h3 {\n      color: #2c3e50;\n      margin-bottom: 10px;\n    }\n    \n    .detail-card .price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #e74c3c;\n    }\n    \n    .service-description-full {\n      background: white;\n      padding: 30px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n      margin-bottom: 40px;\n    }\n    \n    .service-description-full h2,\n    .service-description-full h3 {\n      color: #2c3e50;\n      margin-bottom: 15px;\n    }\n    \n    .service-description-full p {\n      color: #666;\n      line-height: 1.6;\n      margin-bottom: 20px;\n    }\n    \n    .service-description-full ul {\n      list-style: none;\n      padding: 0;\n    }\n    \n    .service-description-full li {\n      padding: 5px 0;\n      color: #666;\n    }\n    \n    .service-description-full li:before {\n      content: \"✓\";\n      color: #27ae60;\n      margin-right: 10px;\n    }\n    \n    .service-booking {\n      background: #f8f9fa;\n      padding: 30px;\n      border-radius: 10px;\n      text-align: center;\n      margin-bottom: 40px;\n    }\n    \n    .service-booking h2 {\n      color: #2c3e50;\n      margin-bottom: 15px;\n    }\n    \n    .service-booking p {\n      color: #666;\n      margin-bottom: 25px;\n    }\n    \n    .booking-actions {\n      display: flex;\n      gap: 15px;\n      justify-content: center;\n      flex-wrap: wrap;\n    }\n    \n    .btn {\n      padding: 12px 24px;\n      border: none;\n      border-radius: 5px;\n      cursor: pointer;\n      text-decoration: none;\n      display: inline-block;\n      text-align: center;\n      transition: all 0.3s ease;\n      font-size: 16px;\n    }\n    \n    .btn-large {\n      padding: 15px 30px;\n      font-size: 18px;\n    }\n    \n    .btn-primary {\n      background: #3498db;\n      color: white;\n    }\n    \n    .btn-primary:hover {\n      background: #2980b9;\n    }\n    \n    .btn-outline {\n      background: transparent;\n      color: #3498db;\n      border: 2px solid #3498db;\n    }\n    \n    .btn-outline:hover {\n      background: #3498db;\n      color: white;\n    }\n    \n    .related-services {\n      margin-top: 60px;\n    }\n    \n    .related-services h2 {\n      text-align: center;\n      color: #2c3e50;\n      margin-bottom: 30px;\n    }\n    \n    .services-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 20px;\n    }\n    \n    .service-card {\n      background: white;\n      padding: 20px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n      text-align: center;\n    }\n    \n    .service-card .service-icon i {\n      font-size: 2rem;\n      color: #3498db;\n      margin-bottom: 15px;\n    }\n    \n    .service-card h3 {\n      color: #2c3e50;\n      margin-bottom: 10px;\n    }\n    \n    .service-card p {\n      color: #666;\n      margin-bottom: 15px;\n    }\n    \n    .service-card .service-price {\n      font-size: 1.2rem;\n      font-weight: bold;\n      color: #e74c3c;\n      margin-bottom: 15px;\n    }\n    \n    .loading, .error {\n      text-align: center;\n      padding: 100px 0;\n    }\n    \n    .error h2 {\n      color: #e74c3c;\n      margin-bottom: 20px;\n    }\n  `]\n})\nexport class ServiceDetailComponent implements OnInit {\n  service: Service | null = null;\n  relatedServices: Service[] = [];\n  error = false;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private serviceService: ServiceService\n  ) {}\n\n  ngOnInit(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.loadService(id);\n      this.loadRelatedServices(id);\n    } else {\n      this.error = true;\n    }\n  }\n\n  private loadService(id: string): void {\n    this.serviceService.getService(id).subscribe({\n      next: (service) => {\n        this.service = service;\n      },\n      error: () => {\n        this.error = true;\n      }\n    });\n  }\n\n  private loadRelatedServices(currentId: string): void {\n    this.serviceService.getServices().subscribe({\n      next: (services) => {\n        this.relatedServices = services\n          .filter(s => s.id !== currentId)\n          .slice(0, 3);\n      },\n      error: () => {\n        // Ignore l'erreur pour les services liés\n      }\n    });\n  }\n\n  getServiceFormat(): string {\n    if (!this.service) return '';\n    \n    switch (this.service.category) {\n      case 'Coaching':\n        return 'Séance individuelle en présentiel ou visio';\n      case 'Atelier':\n        return 'Atelier de groupe en présentiel';\n      case 'Formation':\n        return 'Formation en ligne avec support';\n      default:\n        return 'Format à définir';\n    }\n  }\n\n  getFullDescription(): string {\n    if (!this.service) return '';\n    \n    return `Ce service de ${this.service.category.toLowerCase()} est conçu pour vous accompagner \n    dans votre développement personnel. Avec une approche personnalisée et des outils pratiques, \n    vous développerez les compétences nécessaires pour atteindre vos objectifs et améliorer \n    votre bien-être au quotidien.`;\n  }\n\n  getServiceBenefits(): string[] {\n    if (!this.service) return [];\n    \n    const commonBenefits = [\n      'Développement de la confiance en soi',\n      'Amélioration de la gestion du stress',\n      'Clarification de vos objectifs personnels',\n      'Techniques pratiques applicables au quotidien'\n    ];\n    \n    switch (this.service.category) {\n      case 'Coaching':\n        return [\n          ...commonBenefits,\n          'Accompagnement personnalisé',\n          'Plan d\\'action sur mesure'\n        ];\n      case 'Atelier':\n        return [\n          ...commonBenefits,\n          'Échanges enrichissants avec le groupe',\n          'Exercices pratiques en équipe'\n        ];\n      case 'Formation':\n        return [\n          ...commonBenefits,\n          'Contenu structuré et progressif',\n          'Accès illimité aux ressources'\n        ];\n      default:\n        return commonBenefits;\n    }\n  }\n\n  getTargetAudience(): string {\n    if (!this.service) return '';\n    \n    return `Ce service s'adresse à toute personne souhaitant améliorer son développement personnel, \n    que vous soyez débutant ou que vous ayez déjà une expérience dans ce domaine. \n    Aucun prérequis particulier n'est nécessaire.`;\n  }\n\n  bookService(): void {\n    if (this.service) {\n      // Logique de réservation à implémenter\n      alert(`Réservation pour ${this.service.name} - Vous allez être redirigé vers le formulaire de contact.`);\n      this.router.navigate(['/contact']);\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiCC,YAAY,QAAQ,iBAAiB;;;;;;;;IAsDxDC,EAAA,CAAAC,cAAA,SAAiD;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAa;;;;;IAyB9DN,EADF,CAAAC,cAAA,cAAyE,cAC7C;IACxBD,EAAA,CAAAO,SAAA,QAAqC;IACvCP,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvCH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,iBAGC;IACCD,EAAA,CAAAE,MAAA,wBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAXCH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAQ,UAAA,CAAAC,iBAAA,CAAAC,IAAA,CAA6B;IAE9BV,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAI,iBAAA,CAAAE,IAAA,CAAyB;IAC1BX,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAI,iBAAA,CAAAG,WAAA,CAAgC;IACRZ,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAa,kBAAA,KAAAJ,iBAAA,CAAAK,KAAA,WAA2B;IAGpDd,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAR,iBAAA,CAAAS,EAAA,EAA+C;;;;;IAXrDlB,EADF,CAAAC,cAAA,cAAiE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAmB,UAAA,IAAAC,kDAAA,mBAAyE;IAe7EpB,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfmDH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAe,UAAA,YAAAM,MAAA,CAAAC,eAAA,CAAkB;;;;;;IAlEzEtB,EAHN,CAAAC,cAAA,aAA4C,aACnB,aACG,WACJ;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;IAIFH,EAFJ,CAAAC,cAAA,cAA6B,cACC,eACA;IACxBD,EAAA,CAAAO,SAAA,SAA8B;IAChCP,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAE5DF,EAF4D,CAAAG,YAAA,EAAI,EACxD,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA6B,eACF,UACnB;IAAAD,EAAA,CAAAE,MAAA,kBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;IAEJH,EADF,CAAAC,cAAA,eAAyB,UACnB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IACvCF,EADuC,CAAAG,YAAA,EAAI,EACrC;IAEJH,EADF,CAAAC,cAAA,eAAyB,UACnB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAE/BF,EAF+B,CAAAG,YAAA,EAAI,EAC3B,EACF;IAGJH,EADF,CAAAC,cAAA,eAAsC,UAChC;IAAAD,EAAA,CAAAE,MAAA,iCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAG;IACDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,mCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAmB,UAAA,KAAAI,2CAAA,iBAAiD;IACnDvB,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAC9BF,EAD8B,CAAAG,YAAA,EAAI,EAC5B;IAGJH,EADF,CAAAC,cAAA,eAA6B,UACvB;IAAAD,EAAA,CAAAE,MAAA,gCAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,mFAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEjEH,EADF,CAAAC,cAAA,eAA6B,kBACuC;IAAxBD,EAAA,CAAAwB,UAAA,mBAAAC,+DAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAArB,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASR,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IAC/D9B,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsD;IACpDD,EAAA,CAAAE,MAAA,4BACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAENH,EAAA,CAAAmB,UAAA,KAAAY,4CAAA,kBAAiE;IAoBrE/B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA/EMH,EAAA,CAAAI,SAAA,IAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAW,OAAA,CAAArB,IAAA,CAAkB;IAMjBX,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAQ,UAAA,CAAAa,MAAA,CAAAW,OAAA,CAAAtB,IAAA,CAAsB;IAGrBV,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAW,OAAA,CAAArB,IAAA,CAAkB;IACMX,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAW,OAAA,CAAAC,QAAA,CAAsB;IACnBjC,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAW,OAAA,CAAApB,WAAA,CAAyB;IAOrDZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAW,OAAA,CAAAE,QAAA,CAAsB;IAIRlC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAa,kBAAA,KAAAQ,MAAA,CAAAW,OAAA,CAAAlB,KAAA,WAAoB;IAIlCd,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAc,gBAAA,GAAwB;IAO3BnC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAa,kBAAA,MAAAQ,MAAA,CAAAe,kBAAA,QACF;IAI0BpC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAM,MAAA,CAAAgB,kBAAA,GAAuB;IAI9CrC,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAiB,iBAAA,GAAyB;IAiBDtC,EAAA,CAAAI,SAAA,IAAgC;IAAhCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,eAAA,CAAAiB,MAAA,KAAgC;;;;;IAuBjEvC,EADF,CAAAC,cAAA,cAAgD,QAC3C;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAC7BF,EAD6B,CAAAG,YAAA,EAAI,EAC3B;;;;;IAIFH,EAFJ,CAAAC,cAAA,cAAiC,aACR,SACjB;IAAAD,EAAA,CAAAE,MAAA,8BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,4EAAqE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5EH,EAAA,CAAAC,cAAA,iBAAuD;IACrDD,EAAA,CAAAE,MAAA,4BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;AAkPV,OAAM,MAAOqC,sBAAsB;EAKjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B;IAF9B,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IAPxB,KAAAZ,OAAO,GAAmB,IAAI;IAC9B,KAAAV,eAAe,GAAc,EAAE;IAC/B,KAAAuB,KAAK,GAAG,KAAK;EAMV;EAEHC,QAAQA,CAAA;IACN,MAAM5B,EAAE,GAAG,IAAI,CAACwB,KAAK,CAACK,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI/B,EAAE,EAAE;MACN,IAAI,CAACgC,WAAW,CAAChC,EAAE,CAAC;MACpB,IAAI,CAACiC,mBAAmB,CAACjC,EAAE,CAAC;KAC7B,MAAM;MACL,IAAI,CAAC2B,KAAK,GAAG,IAAI;;EAErB;EAEQK,WAAWA,CAAChC,EAAU;IAC5B,IAAI,CAAC0B,cAAc,CAACQ,UAAU,CAAClC,EAAE,CAAC,CAACmC,SAAS,CAAC;MAC3CC,IAAI,EAAGtB,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACxB,CAAC;MACDa,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACA,KAAK,GAAG,IAAI;MACnB;KACD,CAAC;EACJ;EAEQM,mBAAmBA,CAACI,SAAiB;IAC3C,IAAI,CAACX,cAAc,CAACY,WAAW,EAAE,CAACH,SAAS,CAAC;MAC1CC,IAAI,EAAGG,QAAQ,IAAI;QACjB,IAAI,CAACnC,eAAe,GAAGmC,QAAQ,CAC5BC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAKqC,SAAS,CAAC,CAC/BK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC;MACDf,KAAK,EAAEA,CAAA,KAAK;QACV;MAAA;KAEH,CAAC;EACJ;EAEAV,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE,OAAO,EAAE;IAE5B,QAAQ,IAAI,CAACA,OAAO,CAACC,QAAQ;MAC3B,KAAK,UAAU;QACb,OAAO,4CAA4C;MACrD,KAAK,SAAS;QACZ,OAAO,iCAAiC;MAC1C,KAAK,WAAW;QACd,OAAO,iCAAiC;MAC1C;QACE,OAAO,kBAAkB;;EAE/B;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACJ,OAAO,EAAE,OAAO,EAAE;IAE5B,OAAO,iBAAiB,IAAI,CAACA,OAAO,CAACC,QAAQ,CAAC4B,WAAW,EAAE;;;kCAG7B;EAChC;EAEAxB,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACL,OAAO,EAAE,OAAO,EAAE;IAE5B,MAAM8B,cAAc,GAAG,CACrB,sCAAsC,EACtC,sCAAsC,EACtC,2CAA2C,EAC3C,+CAA+C,CAChD;IAED,QAAQ,IAAI,CAAC9B,OAAO,CAACC,QAAQ;MAC3B,KAAK,UAAU;QACb,OAAO,CACL,GAAG6B,cAAc,EACjB,6BAA6B,EAC7B,2BAA2B,CAC5B;MACH,KAAK,SAAS;QACZ,OAAO,CACL,GAAGA,cAAc,EACjB,uCAAuC,EACvC,+BAA+B,CAChC;MACH,KAAK,WAAW;QACd,OAAO,CACL,GAAGA,cAAc,EACjB,iCAAiC,EACjC,+BAA+B,CAChC;MACH;QACE,OAAOA,cAAc;;EAE3B;EAEAxB,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACN,OAAO,EAAE,OAAO,EAAE;IAE5B,OAAO;;kDAEuC;EAChD;EAEAF,WAAWA,CAAA;IACT,IAAI,IAAI,CAACE,OAAO,EAAE;MAChB;MACA+B,KAAK,CAAC,oBAAoB,IAAI,CAAC/B,OAAO,CAACrB,IAAI,4DAA4D,CAAC;MACxG,IAAI,CAACgC,MAAM,CAACqB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;EAEtC;;;uBArHWxB,sBAAsB,EAAAxC,EAAA,CAAAiE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAnE,EAAA,CAAAiE,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAApE,EAAA,CAAAiE,iBAAA,CAAAI,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAtB9B,sBAAsB;MAAA+B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzE,EAAA,CAAA0E,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1P/BhF,EA5FA,CAAAmB,UAAA,IAAA+D,qCAAA,mBAA4C,IAAAC,qCAAA,iBAwFI,IAAAC,qCAAA,iBAIf;;;UA5FJpF,EAAA,CAAAe,UAAA,SAAAkE,GAAA,CAAAjD,OAAA,CAAa;UAwFpBhC,EAAA,CAAAI,SAAA,EAAwB;UAAxBJ,EAAA,CAAAe,UAAA,UAAAkE,GAAA,CAAAjD,OAAA,KAAAiD,GAAA,CAAApC,KAAA,CAAwB;UAI1B7C,EAAA,CAAAI,SAAA,EAAW;UAAXJ,EAAA,CAAAe,UAAA,SAAAkE,GAAA,CAAApC,KAAA,CAAW;;;qBA9FvB/C,YAAY,EAAAuF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAExF,YAAY,EAAAmE,EAAA,CAAAsB,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}