{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class ContactService {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.endpoint = '/contact';\n  }\n  /**\n   * Soumet une demande de contact\n   */\n  submitContact(contactData) {\n    return this.apiService.post(`${this.endpoint}/submit`, contactData);\n  }\n  /**\n   * Demande un devis pour les services B2B\n   */\n  requestQuote(quoteData) {\n    return this.apiService.post(`${this.endpoint}/quote`, quoteData);\n  }\n  /**\n   * Récupère les informations de contact\n   */\n  getContactInfo() {\n    return this.apiService.get(`${this.endpoint}/info`);\n  }\n  /**\n   * Valide un email\n   */\n  validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n  /**\n   * Valide un numéro de téléphone français\n   */\n  validatePhoneNumber(phone) {\n    const phoneRegex = /^(?:(?:\\+|00)33|0)\\s*[1-9](?:[\\s.-]*\\d{2}){4}$/;\n    return phoneRegex.test(phone);\n  }\n  /**\n   * Formate un numéro de téléphone\n   */\n  formatPhoneNumber(phone) {\n    // Supprime tous les caractères non numériques sauf le +\n    const cleaned = phone.replace(/[^\\d+]/g, '');\n    // Si le numéro commence par 0, on le remplace par +33\n    if (cleaned.startsWith('0')) {\n      return '+33' + cleaned.substring(1);\n    }\n    // Si le numéro commence par 33, on ajoute le +\n    if (cleaned.startsWith('33')) {\n      return '+' + cleaned;\n    }\n    return cleaned;\n  }\n  /**\n   * Envoie un message de contact (alias pour submitContact)\n   */\n  sendMessage(contactData) {\n    return this.submitContact(contactData);\n  }\n  static {\n    this.ɵfac = function ContactService_Factory(t) {\n      return new (t || ContactService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ContactService,\n      factory: ContactService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ContactService", "constructor", "apiService", "endpoint", "submitContact", "contactData", "post", "requestQuote", "quoteData", "getContactInfo", "get", "validateEmail", "email", "emailRegex", "test", "validatePhoneNumber", "phone", "phoneRegex", "formatPhoneNumber", "cleaned", "replace", "startsWith", "substring", "sendMessage", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\services\\contact.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { ApiService } from './api.service';\nimport { ContactForm, QuoteForm, ContactInfo, ContactResponse } from '../models/contact.interface';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ContactService {\n  private readonly endpoint = '/contact';\n\n  constructor(private apiService: ApiService) {}\n\n  /**\n   * Soumet une demande de contact\n   */\n  submitContact(contactData: ContactForm): Observable<ContactResponse> {\n    return this.apiService.post<ContactResponse>(`${this.endpoint}/submit`, contactData);\n  }\n\n  /**\n   * Demande un devis pour les services B2B\n   */\n  requestQuote(quoteData: QuoteForm): Observable<ContactResponse> {\n    return this.apiService.post<ContactResponse>(`${this.endpoint}/quote`, quoteData);\n  }\n\n  /**\n   * Récupère les informations de contact\n   */\n  getContactInfo(): Observable<ContactResponse> {\n    return this.apiService.get<ContactResponse>(`${this.endpoint}/info`);\n  }\n\n  /**\n   * Valide un email\n   */\n  validateEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n\n  /**\n   * Valide un numéro de téléphone français\n   */\n  validatePhoneNumber(phone: string): boolean {\n    const phoneRegex = /^(?:(?:\\+|00)33|0)\\s*[1-9](?:[\\s.-]*\\d{2}){4}$/;\n    return phoneRegex.test(phone);\n  }\n\n  /**\n   * Formate un numéro de téléphone\n   */\n  formatPhoneNumber(phone: string): string {\n    // Supprime tous les caractères non numériques sauf le +\n    const cleaned = phone.replace(/[^\\d+]/g, '');\n    \n    // Si le numéro commence par 0, on le remplace par +33\n    if (cleaned.startsWith('0')) {\n      return '+33' + cleaned.substring(1);\n    }\n    \n    // Si le numéro commence par 33, on ajoute le +\n    if (cleaned.startsWith('33')) {\n      return '+' + cleaned;\n    }\n    \n    return cleaned;\n  }\n\n  /**\n   * Envoie un message de contact (alias pour submitContact)\n   */\n  sendMessage(contactData: any): Observable<any> {\n    return this.submitContact(contactData);\n  }\n}\n"], "mappings": ";;AAQA,OAAM,MAAOA,cAAc;EAGzBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAFb,KAAAC,QAAQ,GAAG,UAAU;EAEO;EAE7C;;;EAGAC,aAAaA,CAACC,WAAwB;IACpC,OAAO,IAAI,CAACH,UAAU,CAACI,IAAI,CAAkB,GAAG,IAAI,CAACH,QAAQ,SAAS,EAAEE,WAAW,CAAC;EACtF;EAEA;;;EAGAE,YAAYA,CAACC,SAAoB;IAC/B,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAkB,GAAG,IAAI,CAACH,QAAQ,QAAQ,EAAEK,SAAS,CAAC;EACnF;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACP,UAAU,CAACQ,GAAG,CAAkB,GAAG,IAAI,CAACP,QAAQ,OAAO,CAAC;EACtE;EAEA;;;EAGAQ,aAAaA,CAACC,KAAa;IACzB,MAAMC,UAAU,GAAG,4BAA4B;IAC/C,OAAOA,UAAU,CAACC,IAAI,CAACF,KAAK,CAAC;EAC/B;EAEA;;;EAGAG,mBAAmBA,CAACC,KAAa;IAC/B,MAAMC,UAAU,GAAG,gDAAgD;IACnE,OAAOA,UAAU,CAACH,IAAI,CAACE,KAAK,CAAC;EAC/B;EAEA;;;EAGAE,iBAAiBA,CAACF,KAAa;IAC7B;IACA,MAAMG,OAAO,GAAGH,KAAK,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IAE5C;IACA,IAAID,OAAO,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;MAC3B,OAAO,KAAK,GAAGF,OAAO,CAACG,SAAS,CAAC,CAAC,CAAC;;IAGrC;IACA,IAAIH,OAAO,CAACE,UAAU,CAAC,IAAI,CAAC,EAAE;MAC5B,OAAO,GAAG,GAAGF,OAAO;;IAGtB,OAAOA,OAAO;EAChB;EAEA;;;EAGAI,WAAWA,CAAClB,WAAgB;IAC1B,OAAO,IAAI,CAACD,aAAa,CAACC,WAAW,CAAC;EACxC;;;uBAnEWL,cAAc,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAd3B,cAAc;MAAA4B,OAAA,EAAd5B,cAAc,CAAA6B,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}