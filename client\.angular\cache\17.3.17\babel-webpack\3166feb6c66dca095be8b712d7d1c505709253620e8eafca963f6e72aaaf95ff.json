{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/service.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = a0 => [\"/services\", a0];\nfunction ServicesComponent_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 6);\n    i0.ɵɵtext(1, \" D\\u00E9couvrez nos services de coaching et d'accompagnement personnalis\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ServicesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 13);\n    i0.ɵɵtext(13, \" En savoir plus \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const service_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(service_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(service_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(service_r1.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", service_r1.price, \"\\u20AC\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(service_r1.duration);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(7, _c0, service_r1.id));\n  }\n}\nfunction ServicesComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"button\", 15);\n    i0.ɵɵtext(2, \" Voir tous nos services \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ServicesComponent {\n  constructor(serviceService) {\n    this.serviceService = serviceService;\n    this.isHomePage = false;\n    this.services = [];\n    this.displayedServices = [];\n  }\n  ngOnInit() {\n    this.loadServices();\n  }\n  loadServices() {\n    this.serviceService.getServices().subscribe({\n      next: services => {\n        this.services = services;\n        this.displayedServices = this.isHomePage ? services.slice(0, 3) : services;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des services:', error);\n        // Fallback avec des données de démonstration\n        this.services = this.getDemoServices();\n        this.displayedServices = this.isHomePage ? this.services.slice(0, 3) : this.services;\n      }\n    });\n  }\n  getDemoServices() {\n    return [{\n      id: '1',\n      name: 'Coaching Individuel',\n      description: 'Séance de coaching personnalisé pour atteindre vos objectifs.',\n      price: 80,\n      duration: '1h',\n      icon: 'fas fa-user',\n      category: 'Coaching',\n      formattedPrice: '80€',\n      type: 'B2C',\n      serviceType: 'Coaching',\n      features: ['Séance personnalisée', 'Suivi individuel'],\n      createdAt: new Date().toISOString()\n    }, {\n      id: '2',\n      name: 'Atelier de Groupe',\n      description: 'Participez à nos ateliers de développement personnel en groupe.',\n      price: 45,\n      duration: '2h',\n      icon: 'fas fa-users',\n      category: 'Atelier',\n      formattedPrice: '45€',\n      type: 'B2C',\n      serviceType: 'Atelier',\n      features: ['Groupe de 8 personnes max', 'Exercices pratiques'],\n      createdAt: new Date().toISOString()\n    }, {\n      id: '3',\n      name: 'Formation en Ligne',\n      description: 'Accédez à nos formations complètes en développement personnel.',\n      price: 120,\n      duration: '6 modules',\n      icon: 'fas fa-laptop',\n      category: 'Formation',\n      formattedPrice: '120€',\n      type: 'B2C',\n      serviceType: 'Formation',\n      features: ['Accès illimité', 'Support inclus'],\n      createdAt: new Date().toISOString()\n    }];\n  }\n  static {\n    this.ɵfac = function ServicesComponent_Factory(t) {\n      return new (t || ServicesComponent)(i0.ɵɵdirectiveInject(i1.ServiceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ServicesComponent,\n      selectors: [[\"app-services\"]],\n      inputs: {\n        isHomePage: \"isHomePage\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 6,\n      consts: [[1, \"services-section\"], [1, \"container\"], [\"class\", \"section-description\", 4, \"ngIf\"], [1, \"services-grid\"], [\"class\", \"service-card\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"section-description\"], [1, \"service-card\"], [1, \"service-icon\"], [1, \"service-content\"], [1, \"service-description\"], [1, \"service-price\"], [1, \"service-duration\"], [1, \"btn\", \"btn-primary\", 3, \"routerLink\"], [1, \"text-center\"], [\"routerLink\", \"/services\", 1, \"btn\", \"btn-outline\"]],\n      template: function ServicesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ServicesComponent_p_4_Template, 2, 0, \"p\", 2);\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵtemplate(6, ServicesComponent_div_6_Template, 14, 9, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, ServicesComponent_div_7_Template, 3, 0, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"home-section\", ctx.isHomePage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.isHomePage ? \"Nos Services\" : \"Tous nos Services\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isHomePage);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.displayedServices);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isHomePage && ctx.services.length > 3);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, i3.RouterLink],\n      styles: [\".services-section[_ngcontent-%COMP%] {\\n  padding: 80px 0;\\n  background: white;\\n}\\n\\n.home-section[_ngcontent-%COMP%] {\\n  padding: 60px 0;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 20px;\\n  color: #2c3e50;\\n  font-size: 2.5rem;\\n}\\n\\n.section-description[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.services-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 30px;\\n  margin-bottom: 40px;\\n}\\n\\n.service-card[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 10px;\\n  padding: 30px;\\n  text-align: center;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.3s ease;\\n}\\n\\n.service-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.service-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.service-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #3498db;\\n}\\n\\n.service-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n  color: #2c3e50;\\n  font-size: 1.5rem;\\n}\\n\\n.service-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  line-height: 1.6;\\n}\\n\\n.service-price[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  color: #e74c3c;\\n  margin-bottom: 10px;\\n}\\n\\n.service-duration[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  font-style: italic;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  text-decoration: none;\\n  display: inline-block;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #2980b9;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #3498db;\\n  border: 2px solid #3498db;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%]:hover {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵclassMap", "service_r1", "icon", "ɵɵtextInterpolate", "name", "description", "ɵɵtextInterpolate1", "price", "duration", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "id", "ServicesComponent", "constructor", "serviceService", "isHomePage", "services", "displayedServices", "ngOnInit", "loadServices", "getServices", "subscribe", "next", "slice", "error", "console", "getDemoServices", "category", "formattedPrice", "type", "serviceType", "features", "createdAt", "Date", "toISOString", "ɵɵdirectiveInject", "i1", "ServiceService", "selectors", "inputs", "standalone", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ServicesComponent_Template", "rf", "ctx", "ɵɵtemplate", "ServicesComponent_p_4_Template", "ServicesComponent_div_6_Template", "ServicesComponent_div_7_Template", "ɵɵclassProp", "length", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\services\\services.component.ts"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ServiceService } from '../../services/service.service';\nimport { Service } from '../../models/service.interface';\n\n@Component({\n  selector: 'app-services',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <section class=\"services-section\" [class.home-section]=\"isHomePage\">\n      <div class=\"container\">\n        <h2>{{ isHomePage ? 'Nos Services' : 'Tous nos Services' }}</h2>\n        <p class=\"section-description\" *ngIf=\"!isHomePage\">\n          Découvrez nos services de coaching et d'accompagnement personnalisé\n        </p>\n        \n        <div class=\"services-grid\">\n          <div class=\"service-card\" *ngFor=\"let service of displayedServices\">\n            <div class=\"service-icon\">\n              <i [class]=\"service.icon\"></i>\n            </div>\n            <div class=\"service-content\">\n              <h3>{{ service.name }}</h3>\n              <p class=\"service-description\">{{ service.description }}</p>\n              <div class=\"service-price\">{{ service.price }}€</div>\n              <div class=\"service-duration\">{{ service.duration }}</div>\n              <button \n                class=\"btn btn-primary\"\n                [routerLink]=\"['/services', service.id]\"\n              >\n                En savoir plus\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"text-center\" *ngIf=\"isHomePage && services.length > 3\">\n          <button class=\"btn btn-outline\" routerLink=\"/services\">\n            Voir tous nos services\n          </button>\n        </div>\n      </div>\n    </section>\n  `,\n  styles: [`\n    .services-section {\n      padding: 80px 0;\n      background: white;\n    }\n    \n    .home-section {\n      padding: 60px 0;\n    }\n    \n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n    \n    h2 {\n      text-align: center;\n      margin-bottom: 20px;\n      color: #2c3e50;\n      font-size: 2.5rem;\n    }\n    \n    .section-description {\n      text-align: center;\n      margin-bottom: 40px;\n      color: #666;\n      font-size: 1.1rem;\n    }\n    \n    .services-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 30px;\n      margin-bottom: 40px;\n    }\n    \n    .service-card {\n      background: #f8f9fa;\n      border-radius: 10px;\n      padding: 30px;\n      text-align: center;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n      transition: transform 0.3s ease;\n    }\n    \n    .service-card:hover {\n      transform: translateY(-5px);\n    }\n    \n    .service-icon {\n      margin-bottom: 20px;\n    }\n    \n    .service-icon i {\n      font-size: 3rem;\n      color: #3498db;\n    }\n    \n    .service-content h3 {\n      margin-bottom: 15px;\n      color: #2c3e50;\n      font-size: 1.5rem;\n    }\n    \n    .service-description {\n      color: #666;\n      margin-bottom: 20px;\n      line-height: 1.6;\n    }\n    \n    .service-price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #e74c3c;\n      margin-bottom: 10px;\n    }\n    \n    .service-duration {\n      color: #666;\n      margin-bottom: 20px;\n      font-style: italic;\n    }\n    \n    .btn {\n      padding: 10px 20px;\n      border: none;\n      border-radius: 5px;\n      cursor: pointer;\n      text-decoration: none;\n      display: inline-block;\n      text-align: center;\n      transition: all 0.3s ease;\n    }\n    \n    .btn-primary {\n      background: #3498db;\n      color: white;\n    }\n    \n    .btn-primary:hover {\n      background: #2980b9;\n    }\n    \n    .btn-outline {\n      background: transparent;\n      color: #3498db;\n      border: 2px solid #3498db;\n    }\n    \n    .btn-outline:hover {\n      background: #3498db;\n      color: white;\n    }\n    \n    .text-center {\n      text-align: center;\n    }\n  `]\n})\nexport class ServicesComponent implements OnInit {\n  @Input() isHomePage: boolean = false;\n  \n  services: Service[] = [];\n  displayedServices: Service[] = [];\n\n  constructor(private serviceService: ServiceService) {}\n\n  ngOnInit(): void {\n    this.loadServices();\n  }\n\n  private loadServices(): void {\n    this.serviceService.getServices().subscribe({\n      next: (services: Service[]) => {\n        this.services = services;\n        this.displayedServices = this.isHomePage\n          ? services.slice(0, 3)\n          : services;\n      },\n      error: (error: any) => {\n        console.error('Erreur lors du chargement des services:', error);\n        // Fallback avec des données de démonstration\n        this.services = this.getDemoServices();\n        this.displayedServices = this.isHomePage\n          ? this.services.slice(0, 3)\n          : this.services;\n      }\n    });\n  }\n\n  private getDemoServices(): Service[] {\n    return [\n      {\n        id: '1',\n        name: 'Coaching Individuel',\n        description: 'Séance de coaching personnalisé pour atteindre vos objectifs.',\n        price: 80,\n        duration: '1h',\n        icon: 'fas fa-user',\n        category: 'Coaching',\n        formattedPrice: '80€',\n        type: 'B2C',\n        serviceType: 'Coaching',\n        features: ['Séance personnalisée', 'Suivi individuel'],\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: '2',\n        name: 'Atelier de Groupe',\n        description: 'Participez à nos ateliers de développement personnel en groupe.',\n        price: 45,\n        duration: '2h',\n        icon: 'fas fa-users',\n        category: 'Atelier',\n        formattedPrice: '45€',\n        type: 'B2C',\n        serviceType: 'Atelier',\n        features: ['Groupe de 8 personnes max', 'Exercices pratiques'],\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: '3',\n        name: 'Formation en Ligne',\n        description: 'Accédez à nos formations complètes en développement personnel.',\n        price: 120,\n        duration: '6 modules',\n        icon: 'fas fa-laptop',\n        category: 'Formation',\n        formattedPrice: '120€',\n        type: 'B2C',\n        serviceType: 'Formation',\n        features: ['Accès illimité', 'Support inclus'],\n        createdAt: new Date().toISOString()\n      }\n    ];\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;;IAYtCC,EAAA,CAAAC,cAAA,WAAmD;IACjDD,EAAA,CAAAE,MAAA,sFACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAIAH,EADF,CAAAC,cAAA,aAAoE,aACxC;IACxBD,EAAA,CAAAI,SAAA,QAA8B;IAChCJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,aAA6B,SACvB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAE,MAAA,wBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAdCH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,UAAA,CAAAC,UAAA,CAAAC,IAAA,CAAsB;IAGrBR,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,iBAAA,CAAAF,UAAA,CAAAG,IAAA,CAAkB;IACSV,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAS,iBAAA,CAAAF,UAAA,CAAAI,WAAA,CAAyB;IAC7BX,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAY,kBAAA,KAAAL,UAAA,CAAAM,KAAA,WAAoB;IACjBb,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAS,iBAAA,CAAAF,UAAA,CAAAO,QAAA,CAAsB;IAGlDd,EAAA,CAAAK,SAAA,EAAwC;IAAxCL,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAV,UAAA,CAAAW,EAAA,EAAwC;;;;;IAS9ClB,EADF,CAAAC,cAAA,cAAmE,iBACV;IACrDD,EAAA,CAAAE,MAAA,+BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;AA4Hd,OAAM,MAAOgB,iBAAiB;EAM5BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IALzB,KAAAC,UAAU,GAAY,KAAK;IAEpC,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,iBAAiB,GAAc,EAAE;EAEoB;EAErDC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAACL,cAAc,CAACM,WAAW,EAAE,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGN,QAAmB,IAAI;QAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACF,UAAU,GACpCC,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACpBP,QAAQ;MACd,CAAC;MACDQ,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D;QACA,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACU,eAAe,EAAE;QACtC,IAAI,CAACT,iBAAiB,GAAG,IAAI,CAACF,UAAU,GACpC,IAAI,CAACC,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACzB,IAAI,CAACP,QAAQ;MACnB;KACD,CAAC;EACJ;EAEQU,eAAeA,CAAA;IACrB,OAAO,CACL;MACEf,EAAE,EAAE,GAAG;MACPR,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,+DAA+D;MAC5EE,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,IAAI;MACdN,IAAI,EAAE,aAAa;MACnB0B,QAAQ,EAAE,UAAU;MACpBC,cAAc,EAAE,KAAK;MACrBC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,UAAU;MACvBC,QAAQ,EAAE,CAAC,sBAAsB,EAAE,kBAAkB,CAAC;MACtDC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,EACD;MACEvB,EAAE,EAAE,GAAG;MACPR,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,iEAAiE;MAC9EE,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,IAAI;MACdN,IAAI,EAAE,cAAc;MACpB0B,QAAQ,EAAE,SAAS;MACnBC,cAAc,EAAE,KAAK;MACrBC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,SAAS;MACtBC,QAAQ,EAAE,CAAC,2BAA2B,EAAE,qBAAqB,CAAC;MAC9DC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,EACD;MACEvB,EAAE,EAAE,GAAG;MACPR,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,gEAAgE;MAC7EE,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,WAAW;MACrBN,IAAI,EAAE,eAAe;MACrB0B,QAAQ,EAAE,WAAW;MACrBC,cAAc,EAAE,MAAM;MACtBC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,WAAW;MACxBC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;MAC9CC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,CACF;EACH;;;uBA5EWtB,iBAAiB,EAAAnB,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAjBzB,iBAAiB;MAAA0B,SAAA;MAAAC,MAAA;QAAAxB,UAAA;MAAA;MAAAyB,UAAA;MAAAT,QAAA,GAAAtC,EAAA,CAAAgD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzJtBtD,EAFJ,CAAAC,cAAA,iBAAoE,aAC3C,SACjB;UAAAD,EAAA,CAAAE,MAAA,GAAuD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAwD,UAAA,IAAAC,8BAAA,eAAmD;UAInDzD,EAAA,CAAAC,cAAA,aAA2B;UACzBD,EAAA,CAAAwD,UAAA,IAAAE,gCAAA,kBAAoE;UAiBtE1D,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAwD,UAAA,IAAAG,gCAAA,iBAAmE;UAMvE3D,EADE,CAAAG,YAAA,EAAM,EACE;;;UAjCwBH,EAAA,CAAA4D,WAAA,iBAAAL,GAAA,CAAAjC,UAAA,CAAiC;UAE3DtB,EAAA,CAAAK,SAAA,GAAuD;UAAvDL,EAAA,CAAAS,iBAAA,CAAA8C,GAAA,CAAAjC,UAAA,wCAAuD;UAC3BtB,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAe,UAAA,UAAAwC,GAAA,CAAAjC,UAAA,CAAiB;UAKDtB,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAe,UAAA,YAAAwC,GAAA,CAAA/B,iBAAA,CAAoB;UAmB1CxB,EAAA,CAAAK,SAAA,EAAuC;UAAvCL,EAAA,CAAAe,UAAA,SAAAwC,GAAA,CAAAjC,UAAA,IAAAiC,GAAA,CAAAhC,QAAA,CAAAsC,MAAA,KAAuC;;;qBA7B7D/D,YAAY,EAAAgE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEjE,YAAY,EAAAkE,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}