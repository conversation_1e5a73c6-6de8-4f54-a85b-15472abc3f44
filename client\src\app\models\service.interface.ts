export interface Service {
  id: string;
  name: string;
  description: string;
  price: number | string;
  formattedPrice?: string;
  type?: 'B2C' | 'B2B';
  serviceType?: string;
  category: string;
  duration: string;
  features?: string[];
  createdAt?: string;
  icon?: string; // Ajout de la propriété icon pour les composants
}

export interface ServiceResponse {
  success: boolean;
  count?: number;
  data: Service | Service[] | ServicesByType;
  error?: string;
  message?: string;
  statistics?: ServiceStatistics;
}

export interface ServicesByType {
  B2C: Service[];
  B2B: Service[];
}

export interface ServiceStatistics {
  totalServices: number;
  b2cCount: number;
  b2bCount: number;
}

export interface ServiceSearchParams {
  q?: string;
  type?: 'B2C' | 'B2B';
  category?: string;
}
