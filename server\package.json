{"name": "disconnect-to-connect-server", "version": "1.0.0", "description": "API Express.js pour Disconnect To Connect", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "morgan": "^1.10.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1"}, "keywords": ["express", "api", "mvc", "e-commerce"], "author": "Disconnect To Connect", "license": "MIT"}