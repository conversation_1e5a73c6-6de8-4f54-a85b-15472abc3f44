{"ast": null, "code": "export const routes = [{\n  path: '',\n  loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent),\n  title: 'Disconnect To Connect - Cartes de Développement Personnel'\n}, {\n  path: 'products',\n  loadComponent: () => import('./components/products/products.component').then(m => m.ProductsComponent),\n  title: 'Nos Cartes - Disconnect To Connect'\n}, {\n  path: 'products/:id',\n  loadComponent: () => import('./components/product-detail/product-detail.component').then(m => m.ProductDetailComponent),\n  title: 'Détail Produit - Disconnect To Connect'\n}, {\n  path: 'services',\n  loadComponent: () => import('./components/services/services.component').then(m => m.ServicesComponent),\n  title: 'Nos Services - Disconnect To Connect'\n}, {\n  path: 'services/:id',\n  loadComponent: () => import('./components/service-detail/service-detail.component').then(m => m.ServiceDetailComponent),\n  title: 'Détail Service - Disconnect To Connect'\n}, {\n  path: 'about',\n  loadComponent: () => import('./components/about/about.component').then(m => m.AboutComponent),\n  title: 'À Propos - Disconnect To Connect'\n}, {\n  path: 'contact',\n  loadComponent: () => import('./components/contact/contact.component').then(m => m.ContactComponent),\n  title: 'Contact - Disconnect To Connect'\n}, {\n  path: '**',\n  redirectTo: ''\n}];", "map": {"version": 3, "names": ["routes", "path", "loadComponent", "then", "m", "HomeComponent", "title", "ProductsComponent", "ProductDetailComponent", "ServicesComponent", "ServiceDetailComponent", "AboutComponent", "ContactComponent", "redirectTo"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent),\n    title: 'Disconnect To Connect - Cartes de Développement Personnel'\n  },\n  {\n    path: 'products',\n    loadComponent: () => import('./components/products/products.component').then(m => m.ProductsComponent),\n    title: 'Nos Cartes - Disconnect To Connect'\n  },\n  {\n    path: 'products/:id',\n    loadComponent: () => import('./components/product-detail/product-detail.component').then(m => m.ProductDetailComponent),\n    title: 'Détail Produit - Disconnect To Connect'\n  },\n  {\n    path: 'services',\n    loadComponent: () => import('./components/services/services.component').then(m => m.ServicesComponent),\n    title: 'Nos Services - Disconnect To Connect'\n  },\n  {\n    path: 'services/:id',\n    loadComponent: () => import('./components/service-detail/service-detail.component').then(m => m.ServiceDetailComponent),\n    title: 'Détail Service - Disconnect To Connect'\n  },\n  {\n    path: 'about',\n    loadComponent: () => import('./components/about/about.component').then(m => m.AboutComponent),\n    title: 'À Propos - Disconnect To Connect'\n  },\n  {\n    path: 'contact',\n    loadComponent: () => import('./components/contact/contact.component').then(m => m.ContactComponent),\n    title: 'Contact - Disconnect To Connect'\n  },\n  {\n    path: '**',\n    redirectTo: ''\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,CAAC;EAC1FC,KAAK,EAAE;CACR,EACD;EACEL,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,iBAAiB,CAAC;EACtGD,KAAK,EAAE;CACR,EACD;EACEL,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,sBAAsB,CAAC;EACvHF,KAAK,EAA<PERSON>;CACR,EACD;EACEL,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,iBAAiB,CAAC;EACtGH,KAAK,EAAE;CACR,EACD;EACEL,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,sBAAsB,CAAC;EACvHJ,KAAK,EAAE;CACR,EACD;EACEL,IAAI,EAAE,OAAO;EACbC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,cAAc,CAAC;EAC7FL,KAAK,EAAE;CACR,EACD;EACEL,IAAI,EAAE,SAAS;EACfC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,gBAAgB,CAAC;EACnGN,KAAK,EAAE;CACR,EACD;EACEL,IAAI,EAAE,IAAI;EACVY,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}