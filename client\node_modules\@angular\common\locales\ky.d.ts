/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: (string | undefined)[];
    BMD: (string | undefined)[];
    BRL: (string | undefined)[];
    BSD: (string | undefined)[];
    BYN: (string | undefined)[];
    BZD: (string | undefined)[];
    CAD: (string | undefined)[];
    DOP: (string | undefined)[];
    EGP: (string | undefined)[];
    GBP: (string | undefined)[];
    HKD: (string | undefined)[];
    HRK: (string | undefined)[];
    ILS: (string | undefined)[];
    INR: (string | undefined)[];
    JMD: (string | undefined)[];
    JPY: string[];
    KGS: string[];
    KRW: (string | undefined)[];
    MXN: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    THB: string[];
    TTD: (string | undefined)[];
    TWD: (string | undefined)[];
    USD: (string | undefined)[];
    XCD: (string | undefined)[];
} | undefined)[];
export default _default;
