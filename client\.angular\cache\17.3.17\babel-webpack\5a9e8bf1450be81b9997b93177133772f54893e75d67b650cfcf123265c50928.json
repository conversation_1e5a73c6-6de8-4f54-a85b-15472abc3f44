{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { HeroComponent } from '../hero/hero.component';\nimport { AboutComponent } from '../about/about.component';\nimport { ProductsComponent } from '../products/products.component';\nimport { ServicesComponent } from '../services/services.component';\nimport { ContactComponent } from '../contact/contact.component';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  ngOnInit() {\n    // Animations d'entrée ou autres initialisations\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 3,\n      consts: [[1, \"home-page\"], [3, \"isHomePage\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-hero\")(2, \"app-about\")(3, \"app-products\", 1)(4, \"app-services\", 1)(5, \"app-contact\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"isHomePage\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"isHomePage\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"isHomePage\", true);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, HeroComponent, AboutComponent, ProductsComponent, ServicesComponent, ContactComponent],\n      styles: [\".home-page[_ngcontent-%COMP%] {\\n  padding-top: 70px; \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9ob21lL2hvbWUuY29tcG9uZW50LnRzIiwid2VicGFjazovLy4vLi4vLi4vU2l0ZSUyMGUtY29tbWVyY2UvY2xpZW50L3NyYy9hcHAvY29tcG9uZW50cy9ob21lL2hvbWUuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsaUJBQUEsRUFBQSw2QkFBQTtBQ0FOIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmhvbWUtcGFnZSB7XG4gICAgICBwYWRkaW5nLXRvcDogNzBweDsgLyogQ29tcGVuc2VyIGxlIGhlYWRlciBmaXhlICovXG4gICAgfVxuICAiLCIuaG9tZS1wYWdlIHtcbiAgcGFkZGluZy10b3A6IDcwcHg7IC8qIENvbXBlbnNlciBsZSBoZWFkZXIgZml4ZSAqL1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "HeroComponent", "AboutComponent", "ProductsComponent", "ServicesComponent", "ContactComponent", "HomeComponent", "ngOnInit", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\home\\home.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { HeroComponent } from '../hero/hero.component';\nimport { AboutComponent } from '../about/about.component';\nimport { ProductsComponent } from '../products/products.component';\nimport { ServicesComponent } from '../services/services.component';\nimport { ContactComponent } from '../contact/contact.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [\n    CommonModule, \n    RouterModule, \n    HeroComponent, \n    AboutComponent, \n    ProductsComponent, \n    ServicesComponent, \n    ContactComponent\n  ],\n  template: `\n    <div class=\"home-page\">\n      <app-hero></app-hero>\n      <app-about></app-about>\n      <app-products [isHomePage]=\"true\"></app-products>\n      <app-services [isHomePage]=\"true\"></app-services>\n      <app-contact [isHomePage]=\"true\"></app-contact>\n    </div>\n  `,\n  styles: [`\n    .home-page {\n      padding-top: 70px; /* Compenser le header fixe */\n    }\n  `]\n})\nexport class HomeComponent implements OnInit {\n\n  ngOnInit(): void {\n    // Animations d'entrée ou autres initialisations\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,gBAAgB,QAAQ,8BAA8B;;AA6B/D,OAAM,MAAOC,aAAa;EAExBC,QAAQA,CAAA;IACN;EAAA;;;uBAHSD,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAdtBP,EAAA,CAAAS,cAAA,aAAuB;UAKrBT,EAJA,CAAAU,SAAA,eAAqB,gBACE,sBAC0B,sBACA,qBACF;UACjDV,EAAA,CAAAW,YAAA,EAAM;;;UAHUX,EAAA,CAAAY,SAAA,GAAmB;UAAnBZ,EAAA,CAAAa,UAAA,oBAAmB;UACnBb,EAAA,CAAAY,SAAA,EAAmB;UAAnBZ,EAAA,CAAAa,UAAA,oBAAmB;UACpBb,EAAA,CAAAY,SAAA,EAAmB;UAAnBZ,EAAA,CAAAa,UAAA,oBAAmB;;;qBAdlCzB,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,cAAc,EACdC,iBAAiB,EACjBC,iBAAiB,EACjBC,gBAAgB;MAAAoB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}