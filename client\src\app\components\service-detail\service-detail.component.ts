import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ServiceService } from '../../services/service.service';
import { Service } from '../../models/service.interface';

@Component({
  selector: 'app-service-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="service-detail" *ngIf="service">
      <div class="container">
        <nav class="breadcrumb">
          <a routerLink="/">Accueil</a>
          <span>/</span>
          <a routerLink="/services">Services</a>
          <span>/</span>
          <span>{{ service.name }}</span>
        </nav>
        
        <div class="service-content">
          <div class="service-header">
            <div class="service-icon">
              <i [class]="service.icon"></i>
            </div>
            <div class="service-info">
              <h1>{{ service.name }}</h1>
              <p class="service-category">{{ service.category }}</p>
              <p class="service-description">{{ service.description }}</p>
            </div>
          </div>
          
          <div class="service-details">
            <div class="detail-card">
              <h3>Durée</h3>
              <p>{{ service.duration }}</p>
            </div>
            <div class="detail-card">
              <h3>Prix</h3>
              <p class="price">{{ service.price }}€</p>
            </div>
            <div class="detail-card">
              <h3>Format</h3>
              <p>{{ getServiceFormat() }}</p>
            </div>
          </div>
          
          <div class="service-description-full">
            <h2>Description complète</h2>
            <p>
              {{ getFullDescription() }}
            </p>
            
            <h3>Ce que vous allez apprendre</h3>
            <ul>
              <li *ngFor="let benefit of getServiceBenefits()">{{ benefit }}</li>
            </ul>
            
            <h3>Pour qui ?</h3>
            <p>{{ getTargetAudience() }}</p>
          </div>
          
          <div class="service-booking">
            <h2>Réserver ce service</h2>
            <p>Prêt à commencer votre parcours de développement personnel ?</p>
            <div class="booking-actions">
              <button class="btn btn-primary btn-large" (click)="bookService()">
                Réserver maintenant
              </button>
              <button class="btn btn-outline" routerLink="/contact">
                Poser une question
              </button>
            </div>
          </div>
        </div>
        
        <div class="related-services" *ngIf="relatedServices.length > 0">
          <h2>Services similaires</h2>
          <div class="services-grid">
            <div class="service-card" *ngFor="let relatedService of relatedServices">
              <div class="service-icon">
                <i [class]="relatedService.icon"></i>
              </div>
              <h3>{{ relatedService.name }}</h3>
              <p>{{ relatedService.description }}</p>
              <div class="service-price">{{ relatedService.price }}€</div>
              <button 
                class="btn btn-primary"
                [routerLink]="['/services', relatedService.id]"
              >
                En savoir plus
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="loading" *ngIf="!service && !error">
      <p>Chargement du service...</p>
    </div>
    
    <div class="error" *ngIf="error">
      <div class="container">
        <h2>Service non trouvé</h2>
        <p>Le service que vous recherchez n'existe pas ou n'est plus disponible.</p>
        <button class="btn btn-primary" routerLink="/services">
          Retour aux services
        </button>
      </div>
    </div>
  `,
  styles: [`
    .service-detail {
      padding: 100px 0 80px;
      min-height: 100vh;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    .breadcrumb {
      margin-bottom: 30px;
      color: #666;
    }
    
    .breadcrumb a {
      color: #3498db;
      text-decoration: none;
    }
    
    .breadcrumb span {
      margin: 0 10px;
    }
    
    .service-header {
      display: flex;
      align-items: center;
      gap: 30px;
      margin-bottom: 40px;
      padding: 30px;
      background: white;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .service-icon i {
      font-size: 4rem;
      color: #3498db;
    }
    
    .service-info h1 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .service-category {
      color: #3498db;
      font-weight: 500;
      margin-bottom: 15px;
    }
    
    .service-description {
      color: #666;
      line-height: 1.6;
    }
    
    .service-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 40px;
    }
    
    .detail-card {
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      text-align: center;
    }
    
    .detail-card h3 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .detail-card .price {
      font-size: 1.5rem;
      font-weight: bold;
      color: #e74c3c;
    }
    
    .service-description-full {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      margin-bottom: 40px;
    }
    
    .service-description-full h2,
    .service-description-full h3 {
      color: #2c3e50;
      margin-bottom: 15px;
    }
    
    .service-description-full p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 20px;
    }
    
    .service-description-full ul {
      list-style: none;
      padding: 0;
    }
    
    .service-description-full li {
      padding: 5px 0;
      color: #666;
    }
    
    .service-description-full li:before {
      content: "✓";
      color: #27ae60;
      margin-right: 10px;
    }
    
    .service-booking {
      background: #f8f9fa;
      padding: 30px;
      border-radius: 10px;
      text-align: center;
      margin-bottom: 40px;
    }
    
    .service-booking h2 {
      color: #2c3e50;
      margin-bottom: 15px;
    }
    
    .service-booking p {
      color: #666;
      margin-bottom: 25px;
    }
    
    .booking-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      text-align: center;
      transition: all 0.3s ease;
      font-size: 16px;
    }
    
    .btn-large {
      padding: 15px 30px;
      font-size: 18px;
    }
    
    .btn-primary {
      background: #3498db;
      color: white;
    }
    
    .btn-primary:hover {
      background: #2980b9;
    }
    
    .btn-outline {
      background: transparent;
      color: #3498db;
      border: 2px solid #3498db;
    }
    
    .btn-outline:hover {
      background: #3498db;
      color: white;
    }
    
    .related-services {
      margin-top: 60px;
    }
    
    .related-services h2 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 30px;
    }
    
    .services-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
    }
    
    .service-card {
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      text-align: center;
    }
    
    .service-card .service-icon i {
      font-size: 2rem;
      color: #3498db;
      margin-bottom: 15px;
    }
    
    .service-card h3 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .service-card p {
      color: #666;
      margin-bottom: 15px;
    }
    
    .service-card .service-price {
      font-size: 1.2rem;
      font-weight: bold;
      color: #e74c3c;
      margin-bottom: 15px;
    }
    
    .loading, .error {
      text-align: center;
      padding: 100px 0;
    }
    
    .error h2 {
      color: #e74c3c;
      margin-bottom: 20px;
    }
  `]
})
export class ServiceDetailComponent implements OnInit {
  service: Service | null = null;
  relatedServices: Service[] = [];
  error = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private serviceService: ServiceService
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadService(id);
      this.loadRelatedServices(id);
    } else {
      this.error = true;
    }
  }

  private loadService(id: string): void {
    this.serviceService.getService(id).subscribe({
      next: (service) => {
        this.service = service;
      },
      error: () => {
        this.error = true;
      }
    });
  }

  private loadRelatedServices(currentId: string): void {
    this.serviceService.getServices().subscribe({
      next: (services) => {
        this.relatedServices = services
          .filter(s => s.id !== currentId)
          .slice(0, 3);
      },
      error: () => {
        // Ignore l'erreur pour les services liés
      }
    });
  }

  getServiceFormat(): string {
    if (!this.service) return '';
    
    switch (this.service.category) {
      case 'Coaching':
        return 'Séance individuelle en présentiel ou visio';
      case 'Atelier':
        return 'Atelier de groupe en présentiel';
      case 'Formation':
        return 'Formation en ligne avec support';
      default:
        return 'Format à définir';
    }
  }

  getFullDescription(): string {
    if (!this.service) return '';
    
    return `Ce service de ${this.service.category.toLowerCase()} est conçu pour vous accompagner 
    dans votre développement personnel. Avec une approche personnalisée et des outils pratiques, 
    vous développerez les compétences nécessaires pour atteindre vos objectifs et améliorer 
    votre bien-être au quotidien.`;
  }

  getServiceBenefits(): string[] {
    if (!this.service) return [];
    
    const commonBenefits = [
      'Développement de la confiance en soi',
      'Amélioration de la gestion du stress',
      'Clarification de vos objectifs personnels',
      'Techniques pratiques applicables au quotidien'
    ];
    
    switch (this.service.category) {
      case 'Coaching':
        return [
          ...commonBenefits,
          'Accompagnement personnalisé',
          'Plan d\'action sur mesure'
        ];
      case 'Atelier':
        return [
          ...commonBenefits,
          'Échanges enrichissants avec le groupe',
          'Exercices pratiques en équipe'
        ];
      case 'Formation':
        return [
          ...commonBenefits,
          'Contenu structuré et progressif',
          'Accès illimité aux ressources'
        ];
      default:
        return commonBenefits;
    }
  }

  getTargetAudience(): string {
    if (!this.service) return '';
    
    return `Ce service s'adresse à toute personne souhaitant améliorer son développement personnel, 
    que vous soyez débutant ou que vous ayez déjà une expérience dans ce domaine. 
    Aucun prérequis particulier n'est nécessaire.`;
  }

  bookService(): void {
    if (this.service) {
      // Logique de réservation à implémenter
      alert(`Réservation pour ${this.service.name} - Vous allez être redirigé vers le formulaire de contact.`);
      this.router.navigate(['/contact']);
    }
  }
}
