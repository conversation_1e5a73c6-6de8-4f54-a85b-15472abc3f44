{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { HeroComponent } from '../hero/hero.component';\nimport { AboutComponent } from '../about/about.component';\nimport { ProductsComponent } from '../products/products.component';\nimport { ServicesComponent } from '../services/services.component';\nimport { ContactComponent } from '../contact/contact.component';\nlet HomeComponent = class HomeComponent {\n  ngOnInit() {\n    // Animations d'entrée ou autres initialisations\n  }\n};\nHomeComponent = __decorate([Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, RouterModule, HeroComponent, AboutComponent, ProductsComponent, ServicesComponent, ContactComponent],\n  template: `\n    <div class=\"home-page\">\n      <app-hero></app-hero>\n      <app-about></app-about>\n      <app-products [isHomePage]=\"true\"></app-products>\n      <app-services [isHomePage]=\"true\"></app-services>\n      <app-contact [isHomePage]=\"true\"></app-contact>\n    </div>\n  `,\n  styles: [`\n    .home-page {\n      padding-top: 70px; /* Compenser le header fixe */\n    }\n  `]\n})], HomeComponent);\nexport { HomeComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "HeroComponent", "AboutComponent", "ProductsComponent", "ServicesComponent", "ContactComponent", "HomeComponent", "ngOnInit", "__decorate", "selector", "standalone", "imports", "template", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\home\\home.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { HeroComponent } from '../hero/hero.component';\nimport { AboutComponent } from '../about/about.component';\nimport { ProductsComponent } from '../products/products.component';\nimport { ServicesComponent } from '../services/services.component';\nimport { ContactComponent } from '../contact/contact.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [\n    CommonModule, \n    RouterModule, \n    HeroComponent, \n    AboutComponent, \n    ProductsComponent, \n    ServicesComponent, \n    ContactComponent\n  ],\n  template: `\n    <div class=\"home-page\">\n      <app-hero></app-hero>\n      <app-about></app-about>\n      <app-products [isHomePage]=\"true\"></app-products>\n      <app-services [isHomePage]=\"true\"></app-services>\n      <app-contact [isHomePage]=\"true\"></app-contact>\n    </div>\n  `,\n  styles: [`\n    .home-page {\n      padding-top: 70px; /* Compenser le header fixe */\n    }\n  `]\n})\nexport class HomeComponent implements OnInit {\n\n  ngOnInit(): void {\n    // Animations d'entrée ou autres initialisations\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,gBAAgB,QAAQ,8BAA8B;AA6BxD,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAExBC,QAAQA,CAAA;IACN;EAAA;CAEH;AALYD,aAAa,GAAAE,UAAA,EA3BzBV,SAAS,CAAC;EACTW,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPZ,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,cAAc,EACdC,iBAAiB,EACjBC,iBAAiB,EACjBC,gBAAgB,CACjB;EACDO,QAAQ,EAAE;;;;;;;;GAQT;EACDC,MAAM,EAAE,CAAC;;;;GAIR;CACF,CAAC,C,EACWP,aAAa,CAKzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}