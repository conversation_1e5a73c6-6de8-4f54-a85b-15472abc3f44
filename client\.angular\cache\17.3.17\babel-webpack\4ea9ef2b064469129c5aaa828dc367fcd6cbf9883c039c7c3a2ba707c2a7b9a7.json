{"ast": null, "code": "import { RouterOutlet } from '@angular/router';\nimport { HeaderComponent } from './components/header/header.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport * as i0 from \"@angular/core\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Disconnect To Connect';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-header\");\n          i0.ɵɵelementStart(1, \"main\");\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"app-footer\");\n        }\n      },\n      dependencies: [RouterOutlet, HeaderComponent, FooterComponent],\n      styles: [\"main[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 140px); \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC50cyIsIndlYnBhY2s6Ly8uLy4uLy4uL1NpdGUlMjBlLWNvbW1lcmNlL2NsaWVudC9zcmMvYXBwL2FwcC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDRSwrQkFBQSxFQUFBLGlEQUFBO0FDQU4iLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICBtYWluIHtcbiAgICAgIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSAxNDBweCk7IC8qIEFqdXN0ZXIgc2Vsb24gbGEgaGF1dGV1ciBkdSBoZWFkZXIgZXQgZm9vdGVyICovXG4gICAgfVxuICAiLCJtYWluIHtcbiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDE0MHB4KTsgLyogQWp1c3RlciBzZWxvbiBsYSBoYXV0ZXVyIGR1IGhlYWRlciBldCBmb290ZXIgKi9cbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterOutlet", "HeaderComponent", "FooterComponent", "AppComponent", "constructor", "title", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { RouterOutlet } from '@angular/router';\nimport { HeaderComponent } from './components/header/header.component';\nimport { FooterComponent } from './components/footer/footer.component';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [RouterOutlet, HeaderComponent, FooterComponent],\n  template: `\n    <app-header></app-header>\n    <main>\n      <router-outlet></router-outlet>\n    </main>\n    <app-footer></app-footer>\n  `,\n  styles: [`\n    main {\n      min-height: calc(100vh - 140px); /* Ajuster selon la hauteur du header et footer */\n    }\n  `]\n})\nexport class AppComponent {\n  title = 'Disconnect To Connect';\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,eAAe,QAAQ,sCAAsC;;AAmBtE,OAAM,MAAOC,YAAY;EAjBzBC,YAAA;IAkBE,KAAAC,KAAK,GAAG,uBAAuB;;;;uBADpBF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAZrBN,EAAA,CAAAQ,SAAA,iBAAyB;UACzBR,EAAA,CAAAS,cAAA,WAAM;UACJT,EAAA,CAAAQ,SAAA,oBAA+B;UACjCR,EAAA,CAAAU,YAAA,EAAO;UACPV,EAAA,CAAAQ,SAAA,iBAAyB;;;qBANjBjB,YAAY,EAAEC,eAAe,EAAEC,eAAe;MAAAkB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}