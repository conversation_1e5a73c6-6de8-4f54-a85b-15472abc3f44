export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  formattedPrice?: string;
  category: string;
  tags?: string[];
  image: string;
  inStock: boolean;
  available?: boolean;
  createdAt?: string;
}

export interface ProductResponse {
  success: boolean;
  count?: number;
  data: Product | Product[];
  error?: string;
  message?: string;
}

export interface ProductSearchParams {
  q?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
}

export interface ProductStatistics {
  totalProducts: number;
  totalCategories: number;
  averagePrice: number;
}
