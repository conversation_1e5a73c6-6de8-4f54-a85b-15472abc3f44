import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ServiceService } from '../../services/service.service';
import { Service } from '../../models/service.interface';

@Component({
  selector: 'app-services',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <section class="services-section" [class.home-section]="isHomePage">
      <div class="container">
        <h2>{{ isHomePage ? 'Nos Services' : 'Tous nos Services' }}</h2>
        <p class="section-description" *ngIf="!isHomePage">
          Découvrez nos services de coaching et d'accompagnement personnalisé
        </p>
        
        <div class="services-grid">
          <div class="service-card" *ngFor="let service of displayedServices">
            <div class="service-icon">
              <i [class]="service.icon"></i>
            </div>
            <div class="service-content">
              <h3>{{ service.name }}</h3>
              <p class="service-description">{{ service.description }}</p>
              <div class="service-price">{{ service.price }}€</div>
              <div class="service-duration">{{ service.duration }}</div>
              <button 
                class="btn btn-primary"
                [routerLink]="['/services', service.id]"
              >
                En savoir plus
              </button>
            </div>
          </div>
        </div>
        
        <div class="text-center" *ngIf="isHomePage && services.length > 3">
          <button class="btn btn-outline" routerLink="/services">
            Voir tous nos services
          </button>
        </div>
      </div>
    </section>
  `,
  styles: [`
    .services-section {
      padding: 80px 0;
      background: white;
    }
    
    .home-section {
      padding: 60px 0;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    h2 {
      text-align: center;
      margin-bottom: 20px;
      color: #2c3e50;
      font-size: 2.5rem;
    }
    
    .section-description {
      text-align: center;
      margin-bottom: 40px;
      color: #666;
      font-size: 1.1rem;
    }
    
    .services-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
      margin-bottom: 40px;
    }
    
    .service-card {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 30px;
      text-align: center;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }
    
    .service-card:hover {
      transform: translateY(-5px);
    }
    
    .service-icon {
      margin-bottom: 20px;
    }
    
    .service-icon i {
      font-size: 3rem;
      color: #3498db;
    }
    
    .service-content h3 {
      margin-bottom: 15px;
      color: #2c3e50;
      font-size: 1.5rem;
    }
    
    .service-description {
      color: #666;
      margin-bottom: 20px;
      line-height: 1.6;
    }
    
    .service-price {
      font-size: 1.5rem;
      font-weight: bold;
      color: #e74c3c;
      margin-bottom: 10px;
    }
    
    .service-duration {
      color: #666;
      margin-bottom: 20px;
      font-style: italic;
    }
    
    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      text-align: center;
      transition: all 0.3s ease;
    }
    
    .btn-primary {
      background: #3498db;
      color: white;
    }
    
    .btn-primary:hover {
      background: #2980b9;
    }
    
    .btn-outline {
      background: transparent;
      color: #3498db;
      border: 2px solid #3498db;
    }
    
    .btn-outline:hover {
      background: #3498db;
      color: white;
    }
    
    .text-center {
      text-align: center;
    }
  `]
})
export class ServicesComponent implements OnInit {
  @Input() isHomePage: boolean = false;
  
  services: Service[] = [];
  displayedServices: Service[] = [];

  constructor(private serviceService: ServiceService) {}

  ngOnInit(): void {
    this.loadServices();
  }

  private loadServices(): void {
    this.serviceService.getServices().subscribe({
      next: (services: Service[]) => {
        this.services = services;
        this.displayedServices = this.isHomePage
          ? services.slice(0, 3)
          : services;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des services:', error);
        // Fallback avec des données de démonstration
        this.services = this.getDemoServices();
        this.displayedServices = this.isHomePage
          ? this.services.slice(0, 3)
          : this.services;
      }
    });
  }

  private getDemoServices(): Service[] {
    return [
      {
        id: '1',
        name: 'Coaching Individuel',
        description: 'Séance de coaching personnalisé pour atteindre vos objectifs.',
        price: 80,
        duration: '1h',
        icon: 'fas fa-user',
        category: 'Coaching',
        formattedPrice: '80€',
        type: 'B2C',
        serviceType: 'Coaching',
        features: ['Séance personnalisée', 'Suivi individuel'],
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Atelier de Groupe',
        description: 'Participez à nos ateliers de développement personnel en groupe.',
        price: 45,
        duration: '2h',
        icon: 'fas fa-users',
        category: 'Atelier',
        formattedPrice: '45€',
        type: 'B2C',
        serviceType: 'Atelier',
        features: ['Groupe de 8 personnes max', 'Exercices pratiques'],
        createdAt: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Formation en Ligne',
        description: 'Accédez à nos formations complètes en développement personnel.',
        price: 120,
        duration: '6 modules',
        icon: 'fas fa-laptop',
        category: 'Formation',
        formattedPrice: '120€',
        type: 'B2C',
        serviceType: 'Formation',
        features: ['Accès illimité', 'Support inclus'],
        createdAt: new Date().toISOString()
      }
    ];
  }
}
