{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = () => ({\n  exact: true\n});\nexport class HeaderComponent {\n  constructor() {\n    this.isScrolled = false;\n    this.isMobileMenuOpen = false;\n  }\n  ngOnInit() {\n    this.checkScroll();\n  }\n  onWindowScroll() {\n    this.checkScroll();\n  }\n  checkScroll() {\n    this.isScrolled = window.pageYOffset > 50;\n  }\n  toggleMobileMenu() {\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\n  }\n  closeMobileMenu() {\n    this.isMobileMenuOpen = false;\n  }\n  scrollToSection(sectionId) {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth'\n      });\n      this.closeMobileMenu();\n    }\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      hostBindings: function HeaderComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function HeaderComponent_scroll_HostBindingHandler() {\n            return ctx.onWindowScroll();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 51,\n      vars: 12,\n      consts: [[1, \"header\"], [1, \"container\"], [1, \"navbar\"], [1, \"navbar-brand\"], [\"routerLink\", \"/\", 1, \"logo\", 3, \"click\"], [1, \"logo-text\"], [1, \"navbar-nav\", \"desktop-nav\"], [1, \"nav-item\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLinkActiveOptions\"], [\"routerLink\", \"/about\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"nav-item\", \"dropdown\"], [\"routerLink\", \"/products\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/services\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/contact\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"navbar-cta\", \"desktop-nav\"], [\"routerLink\", \"/contact\", 1, \"btn\", \"btn-primary\"], [\"aria-label\", \"Menu mobile\", 1, \"mobile-menu-btn\", \"mobile-nav\", 3, \"click\"], [1, \"hamburger-line\"], [1, \"mobile-menu\"], [1, \"mobile-nav-list\"], [1, \"mobile-nav-item\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 1, \"mobile-nav-link\", 3, \"click\", \"routerLinkActiveOptions\"], [\"routerLink\", \"/about\", \"routerLinkActive\", \"active\", 1, \"mobile-nav-link\", 3, \"click\"], [\"routerLink\", \"/products\", \"routerLinkActive\", \"active\", 1, \"mobile-nav-link\", 3, \"click\"], [\"routerLink\", \"/services\", \"routerLinkActive\", \"active\", 1, \"mobile-nav-link\", 3, \"click\"], [\"routerLink\", \"/contact\", \"routerLinkActive\", \"active\", 1, \"mobile-nav-link\", 3, \"click\"], [\"routerLink\", \"/contact\", 1, \"btn\", \"btn-primary\", \"btn-block\", 3, \"click\"], [1, \"mobile-overlay\", 3, \"click\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"nav\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_4_listener() {\n            return ctx.closeMobileMenu();\n          });\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6, \"Disconnect To Connect\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"ul\", 6)(8, \"li\", 7)(9, \"a\", 8);\n          i0.ɵɵtext(10, \" Accueil \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"li\", 7)(12, \"a\", 9);\n          i0.ɵɵtext(13, \" \\u00C0 Propos \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"li\", 10)(15, \"a\", 11);\n          i0.ɵɵtext(16, \" Cartes \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"li\", 10)(18, \"a\", 12);\n          i0.ɵɵtext(19, \" Services \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"li\", 7)(21, \"a\", 13);\n          i0.ɵɵtext(22, \" Contact \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"a\", 15);\n          i0.ɵɵtext(25, \" Nous Contacter \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_26_listener() {\n            return ctx.toggleMobileMenu();\n          });\n          i0.ɵɵelement(27, \"span\", 17)(28, \"span\", 17)(29, \"span\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 18)(31, \"ul\", 19)(32, \"li\", 20)(33, \"a\", 21);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_33_listener() {\n            return ctx.closeMobileMenu();\n          });\n          i0.ɵɵtext(34, \" Accueil \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"li\", 20)(36, \"a\", 22);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_36_listener() {\n            return ctx.closeMobileMenu();\n          });\n          i0.ɵɵtext(37, \" \\u00C0 Propos \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\", 20)(39, \"a\", 23);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_39_listener() {\n            return ctx.closeMobileMenu();\n          });\n          i0.ɵɵtext(40, \" Cartes \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\", 20)(42, \"a\", 24);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_42_listener() {\n            return ctx.closeMobileMenu();\n          });\n          i0.ɵɵtext(43, \" Services \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 20)(45, \"a\", 25);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_45_listener() {\n            return ctx.closeMobileMenu();\n          });\n          i0.ɵɵtext(46, \" Contact \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"li\", 20)(48, \"a\", 26);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_48_listener() {\n            return ctx.closeMobileMenu();\n          });\n          i0.ɵɵtext(49, \" Nous Contacter \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(50, \"div\", 27);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_div_click_50_listener() {\n            return ctx.closeMobileMenu();\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"scrolled\", ctx.isScrolled);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(10, _c0));\n          i0.ɵɵadvance(17);\n          i0.ɵɵclassProp(\"active\", ctx.isMobileMenuOpen);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isMobileMenuOpen);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(11, _c0));\n          i0.ɵɵadvance(17);\n          i0.ɵɵclassProp(\"active\", ctx.isMobileMenuOpen);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, i1.RouterLink, i1.RouterLinkActive],\n      styles: [\".header[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-bottom: 1px solid rgba(63, 25, 0, 0.1);\\n  transition: var(--transition-normal);\\n}\\n.header.scrolled[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.98);\\n  box-shadow: 0 2px 20px rgba(63, 25, 0, 0.1);\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: var(--spacing-sm) 0;\\n  min-height: 70px;\\n}\\n\\n.navbar-brand[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n.navbar-brand[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  background: var(--gradient-primary);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.navbar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n  gap: var(--spacing-lg);\\n}\\n.navbar-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.navbar-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: var(--text-dark);\\n  text-decoration: none;\\n  font-weight: 500;\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--border-radius-sm);\\n  transition: var(--transition-fast);\\n  position: relative;\\n}\\n.navbar-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  background: rgba(63, 25, 0, 0.05);\\n}\\n.navbar-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n.navbar-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 20px;\\n  height: 2px;\\n  background: var(--gradient-primary);\\n  border-radius: 1px;\\n}\\n\\n.navbar-cta[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xs) var(--spacing-md);\\n  font-size: 0.9rem;\\n}\\n\\n.mobile-nav[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.mobile-menu-btn[_ngcontent-%COMP%] {\\n  display: none;\\n  flex-direction: column;\\n  justify-content: center;\\n  width: 30px;\\n  height: 30px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 0;\\n}\\n.mobile-menu-btn[_ngcontent-%COMP%]   .hamburger-line[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 2px;\\n  background: var(--primary-color);\\n  margin: 3px 0;\\n  transition: var(--transition-fast);\\n  transform-origin: center;\\n}\\n.mobile-menu-btn.active[_ngcontent-%COMP%]   .hamburger-line[_ngcontent-%COMP%]:nth-child(1) {\\n  transform: rotate(45deg) translate(6px, 6px);\\n}\\n.mobile-menu-btn.active[_ngcontent-%COMP%]   .hamburger-line[_ngcontent-%COMP%]:nth-child(2) {\\n  opacity: 0;\\n}\\n.mobile-menu-btn.active[_ngcontent-%COMP%]   .hamburger-line[_ngcontent-%COMP%]:nth-child(3) {\\n  transform: rotate(-45deg) translate(6px, -6px);\\n}\\n\\n.mobile-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 70px;\\n  right: -100%;\\n  width: 280px;\\n  height: calc(100vh - 70px);\\n  background: var(--white);\\n  box-shadow: -5px 0 20px rgba(63, 25, 0, 0.1);\\n  transition: var(--transition-normal);\\n  overflow-y: auto;\\n  z-index: 999;\\n}\\n.mobile-menu.active[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.mobile-nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  margin: 0;\\n  padding: var(--spacing-lg);\\n}\\n.mobile-nav-list[_ngcontent-%COMP%]   .mobile-nav-item[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-sm);\\n}\\n.mobile-nav-list[_ngcontent-%COMP%]   .mobile-nav-item[_ngcontent-%COMP%]   .mobile-nav-link[_ngcontent-%COMP%] {\\n  display: block;\\n  color: var(--text-dark);\\n  text-decoration: none;\\n  font-weight: 500;\\n  padding: var(--spacing-sm);\\n  border-radius: var(--border-radius-md);\\n  transition: var(--transition-fast);\\n}\\n.mobile-nav-list[_ngcontent-%COMP%]   .mobile-nav-item[_ngcontent-%COMP%]   .mobile-nav-link[_ngcontent-%COMP%]:hover {\\n  background: rgba(63, 25, 0, 0.05);\\n  color: var(--primary-color);\\n}\\n.mobile-nav-list[_ngcontent-%COMP%]   .mobile-nav-item[_ngcontent-%COMP%]   .mobile-nav-link.active[_ngcontent-%COMP%] {\\n  background: var(--gradient-accent);\\n  color: var(--primary-color);\\n}\\n.mobile-nav-list[_ngcontent-%COMP%]   .mobile-nav-item[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-md);\\n  width: 100%;\\n  justify-content: center;\\n}\\n\\n.mobile-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 70px;\\n  left: 0;\\n  width: 100%;\\n  height: calc(100vh - 70px);\\n  background: rgba(0, 0, 0, 0.5);\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: var(--transition-normal);\\n  z-index: 998;\\n}\\n.mobile-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n@media (max-width: 768px) {\\n  .desktop-nav[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .mobile-nav[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .mobile-menu-btn[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .navbar-brand[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .mobile-menu[_ngcontent-%COMP%] {\\n    width: 100%;\\n    right: -100%;\\n  }\\n  .mobile-menu.active[_ngcontent-%COMP%] {\\n    right: 0;\\n  }\\n  .navbar-brand[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "HeaderComponent", "constructor", "isScrolled", "isMobileMenuOpen", "ngOnInit", "checkScroll", "onWindowScroll", "window", "pageYOffset", "toggleMobileMenu", "closeMobileMenu", "scrollToSection", "sectionId", "element", "document", "getElementById", "scrollIntoView", "behavior", "selectors", "hostBindings", "HeaderComponent_HostBindings", "rf", "ctx", "i0", "ɵɵlistener", "HeaderComponent_scroll_HostBindingHandler", "ɵɵresolveWindow", "ɵɵelementStart", "HeaderComponent_Template_a_click_4_listener", "ɵɵtext", "ɵɵelementEnd", "HeaderComponent_Template_button_click_26_listener", "ɵɵelement", "HeaderComponent_Template_a_click_33_listener", "HeaderComponent_Template_a_click_36_listener", "HeaderComponent_Template_a_click_39_listener", "HeaderComponent_Template_a_click_42_listener", "HeaderComponent_Template_a_click_45_listener", "HeaderComponent_Template_a_click_48_listener", "HeaderComponent_Template_div_click_50_listener", "ɵɵclassProp", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "i1", "RouterLink", "RouterLinkActive", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\header\\header.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\header\\header.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './header.component.html',\n  styleUrls: ['./header.component.scss']\n})\nexport class HeaderComponent implements OnInit {\n  isScrolled = false;\n  isMobileMenuOpen = false;\n\n  ngOnInit() {\n    this.checkScroll();\n  }\n\n  @HostListener('window:scroll', [])\n  onWindowScroll() {\n    this.checkScroll();\n  }\n\n  private checkScroll() {\n    this.isScrolled = window.pageYOffset > 50;\n  }\n\n  toggleMobileMenu() {\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\n  }\n\n  closeMobileMenu() {\n    this.isMobileMenuOpen = false;\n  }\n\n  scrollToSection(sectionId: string) {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      this.closeMobileMenu();\n    }\n  }\n}\n", "<header class=\"header\" [class.scrolled]=\"isScrolled\">\n  <div class=\"container\">\n    <nav class=\"navbar\">\n      <!-- Logo -->\n      <div class=\"navbar-brand\">\n        <a routerLink=\"/\" class=\"logo\" (click)=\"closeMobileMenu()\">\n          <span class=\"logo-text\">Disconnect To Connect</span>\n        </a>\n      </div>\n\n      <!-- Navigation Desktop -->\n      <ul class=\"navbar-nav desktop-nav\">\n        <li class=\"nav-item\">\n          <a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact: true}\" class=\"nav-link\">\n            Accueil\n          </a>\n        </li>\n        <li class=\"nav-item\">\n          <a routerLink=\"/about\" routerLinkActive=\"active\" class=\"nav-link\">\n            À Propos\n          </a>\n        </li>\n        <li class=\"nav-item dropdown\">\n          <a routerLink=\"/products\" routerLinkActive=\"active\" class=\"nav-link\">\n            Cartes\n          </a>\n        </li>\n        <li class=\"nav-item dropdown\">\n          <a routerLink=\"/services\" routerLinkActive=\"active\" class=\"nav-link\">\n            Services\n          </a>\n        </li>\n        <li class=\"nav-item\">\n          <a routerLink=\"/contact\" routerLinkActive=\"active\" class=\"nav-link\">\n            Contact\n          </a>\n        </li>\n      </ul>\n\n      <!-- CTA Button -->\n      <div class=\"navbar-cta desktop-nav\">\n        <a routerLink=\"/contact\" class=\"btn btn-primary\">\n          Nous Contacter\n        </a>\n      </div>\n\n      <!-- Mobile Menu Button -->\n      <button \n        class=\"mobile-menu-btn mobile-nav\" \n        (click)=\"toggleMobileMenu()\"\n        [class.active]=\"isMobileMenuOpen\"\n        aria-label=\"Menu mobile\"\n      >\n        <span class=\"hamburger-line\"></span>\n        <span class=\"hamburger-line\"></span>\n        <span class=\"hamburger-line\"></span>\n      </button>\n    </nav>\n\n    <!-- Navigation Mobile -->\n    <div class=\"mobile-menu\" [class.active]=\"isMobileMenuOpen\">\n      <ul class=\"mobile-nav-list\">\n        <li class=\"mobile-nav-item\">\n          <a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact: true}\" \n             class=\"mobile-nav-link\" (click)=\"closeMobileMenu()\">\n            Accueil\n          </a>\n        </li>\n        <li class=\"mobile-nav-item\">\n          <a routerLink=\"/about\" routerLinkActive=\"active\" \n             class=\"mobile-nav-link\" (click)=\"closeMobileMenu()\">\n            À Propos\n          </a>\n        </li>\n        <li class=\"mobile-nav-item\">\n          <a routerLink=\"/products\" routerLinkActive=\"active\" \n             class=\"mobile-nav-link\" (click)=\"closeMobileMenu()\">\n            Cartes\n          </a>\n        </li>\n        <li class=\"mobile-nav-item\">\n          <a routerLink=\"/services\" routerLinkActive=\"active\" \n             class=\"mobile-nav-link\" (click)=\"closeMobileMenu()\">\n            Services\n          </a>\n        </li>\n        <li class=\"mobile-nav-item\">\n          <a routerLink=\"/contact\" routerLinkActive=\"active\" \n             class=\"mobile-nav-link\" (click)=\"closeMobileMenu()\">\n            Contact\n          </a>\n        </li>\n        <li class=\"mobile-nav-item\">\n          <a routerLink=\"/contact\" class=\"btn btn-primary btn-block\" (click)=\"closeMobileMenu()\">\n            Nous Contacter\n          </a>\n        </li>\n      </ul>\n    </div>\n  </div>\n\n  <!-- Overlay pour fermer le menu mobile -->\n  <div class=\"mobile-overlay\" [class.active]=\"isMobileMenuOpen\" (click)=\"closeMobileMenu()\"></div>\n</header>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;AAS9C,OAAM,MAAOC,eAAe;EAP5BC,YAAA;IAQE,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;;EAExBC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACD,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACH,UAAU,GAAGK,MAAM,CAACC,WAAW,GAAG,EAAE;EAC3C;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACN,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEAO,eAAeA,CAAA;IACb,IAAI,CAACP,gBAAgB,GAAG,KAAK;EAC/B;EAEAQ,eAAeA,CAACC,SAAiB;IAC/B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC9C,IAAI,CAACP,eAAe,EAAE;;EAE1B;;;uBA/BWV,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAkB,SAAA;MAAAC,YAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAfE,EAAA,CAAAC,UAAA,oBAAAC,0CAAA;YAAA,OAAAH,GAAA,CAAAhB,cAAA,EAAgB;UAAA,UAAAiB,EAAA,CAAAG,eAAA,CAAD;;;;;;;;;;UCNpBH,EALR,CAAAI,cAAA,gBAAqD,aAC5B,aACD,aAEQ,WACmC;UAA5BJ,EAAA,CAAAC,UAAA,mBAAAI,4CAAA;YAAA,OAASN,GAAA,CAAAZ,eAAA,EAAiB;UAAA,EAAC;UACxDa,EAAA,CAAAI,cAAA,cAAwB;UAAAJ,EAAA,CAAAM,MAAA,4BAAqB;UAEjDN,EAFiD,CAAAO,YAAA,EAAO,EAClD,EACA;UAKFP,EAFJ,CAAAI,cAAA,YAAmC,YACZ,WACoF;UACrGJ,EAAA,CAAAM,MAAA,iBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,aAAqB,YAC+C;UAChEJ,EAAA,CAAAM,MAAA,uBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,cAA8B,aACyC;UACnEJ,EAAA,CAAAM,MAAA,gBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,cAA8B,aACyC;UACnEJ,EAAA,CAAAM,MAAA,kBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,aAAqB,aACiD;UAClEJ,EAAA,CAAAM,MAAA,iBACF;UAEJN,EAFI,CAAAO,YAAA,EAAI,EACD,EACF;UAIHP,EADF,CAAAI,cAAA,eAAoC,aACe;UAC/CJ,EAAA,CAAAM,MAAA,wBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACA;UAGNP,EAAA,CAAAI,cAAA,kBAKC;UAHCJ,EAAA,CAAAC,UAAA,mBAAAO,kDAAA;YAAA,OAAST,GAAA,CAAAb,gBAAA,EAAkB;UAAA,EAAC;UAM5Bc,EAFA,CAAAS,SAAA,gBAAoC,gBACA,gBACA;UAExCT,EADE,CAAAO,YAAA,EAAS,EACL;UAMAP,EAHN,CAAAI,cAAA,eAA2D,cAC7B,cACE,aAE6B;UAA5BJ,EAAA,CAAAC,UAAA,mBAAAS,6CAAA;YAAA,OAASX,GAAA,CAAAZ,eAAA,EAAiB;UAAA,EAAC;UACpDa,EAAA,CAAAM,MAAA,iBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,cAA4B,aAE6B;UAA5BJ,EAAA,CAAAC,UAAA,mBAAAU,6CAAA;YAAA,OAASZ,GAAA,CAAAZ,eAAA,EAAiB;UAAA,EAAC;UACpDa,EAAA,CAAAM,MAAA,uBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,cAA4B,aAE6B;UAA5BJ,EAAA,CAAAC,UAAA,mBAAAW,6CAAA;YAAA,OAASb,GAAA,CAAAZ,eAAA,EAAiB;UAAA,EAAC;UACpDa,EAAA,CAAAM,MAAA,gBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,cAA4B,aAE6B;UAA5BJ,EAAA,CAAAC,UAAA,mBAAAY,6CAAA;YAAA,OAASd,GAAA,CAAAZ,eAAA,EAAiB;UAAA,EAAC;UACpDa,EAAA,CAAAM,MAAA,kBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,cAA4B,aAE6B;UAA5BJ,EAAA,CAAAC,UAAA,mBAAAa,6CAAA;YAAA,OAASf,GAAA,CAAAZ,eAAA,EAAiB;UAAA,EAAC;UACpDa,EAAA,CAAAM,MAAA,iBACF;UACFN,EADE,CAAAO,YAAA,EAAI,EACD;UAEHP,EADF,CAAAI,cAAA,cAA4B,aAC6D;UAA5BJ,EAAA,CAAAC,UAAA,mBAAAc,6CAAA;YAAA,OAAShB,GAAA,CAAAZ,eAAA,EAAiB;UAAA,EAAC;UACpFa,EAAA,CAAAM,MAAA,wBACF;UAIRN,EAJQ,CAAAO,YAAA,EAAI,EACD,EACF,EACD,EACF;UAGNP,EAAA,CAAAI,cAAA,eAA0F;UAA5BJ,EAAA,CAAAC,UAAA,mBAAAe,+CAAA;YAAA,OAASjB,GAAA,CAAAZ,eAAA,EAAiB;UAAA,EAAC;UAC3Fa,EAD4F,CAAAO,YAAA,EAAM,EACzF;;;UAvGcP,EAAA,CAAAiB,WAAA,aAAAlB,GAAA,CAAApB,UAAA,CAA6B;UAaEqB,EAAA,CAAAkB,SAAA,GAAyC;UAAzClB,EAAA,CAAAmB,UAAA,4BAAAnB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAyC;UAqCvFrB,EAAA,CAAAkB,SAAA,IAAiC;UAAjClB,EAAA,CAAAiB,WAAA,WAAAlB,GAAA,CAAAnB,gBAAA,CAAiC;UAUZoB,EAAA,CAAAkB,SAAA,GAAiC;UAAjClB,EAAA,CAAAiB,WAAA,WAAAlB,GAAA,CAAAnB,gBAAA,CAAiC;UAGRoB,EAAA,CAAAkB,SAAA,GAAyC;UAAzClB,EAAA,CAAAmB,UAAA,4BAAAnB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,EAAyC;UAuCjErB,EAAA,CAAAkB,SAAA,IAAiC;UAAjClB,EAAA,CAAAiB,WAAA,WAAAlB,GAAA,CAAAnB,gBAAA,CAAiC;;;qBD/FnDL,YAAY,EAAEC,YAAY,EAAA8C,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}