import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Service, ServiceResponse, ServiceSearchParams, ServicesByType } from '../models/service.interface';

@Injectable({
  providedIn: 'root'
})
export class ServiceService {
  private readonly endpoint = '/services';

  constructor(private apiService: ApiService) {}

  /**
   * Récupère tous les services
   */
  getAllServices(params?: { type?: 'B2C' | 'B2B'; category?: string }): Observable<ServiceResponse> {
    return this.apiService.get<ServiceResponse>(this.endpoint, params);
  }

  /**
   * Récupère un service par son ID
   */
  getServiceById(id: string): Observable<ServiceResponse> {
    return this.apiService.get<ServiceResponse>(`${this.endpoint}/${id}`);
  }

  /**
   * Récupère les services B2C (Particuliers)
   */
  getB2CServices(): Observable<ServiceResponse> {
    return this.apiService.get<ServiceResponse>(`${this.endpoint}/b2c`);
  }

  /**
   * Récupère les services B2B (Entreprises)
   */
  getB2BServices(): Observable<ServiceResponse> {
    return this.apiService.get<ServiceResponse>(`${this.endpoint}/b2b`);
  }

  /**
   * Récupère toutes les catégories de services
   */
  getCategories(): Observable<{ success: boolean; count: number; data: string[] }> {
    return this.apiService.get<{ success: boolean; count: number; data: string[] }>(`${this.endpoint}/categories`);
  }

  /**
   * Récupère les services groupés par type (B2C/B2B)
   */
  getServicesByType(): Observable<ServiceResponse> {
    return this.apiService.get<ServiceResponse>(`${this.endpoint}/by-type`);
  }

  /**
   * Recherche des services
   */
  searchServices(params: ServiceSearchParams): Observable<{
    success: boolean;
    query: string;
    count: number;
    data: Service[];
  }> {
    return this.apiService.get<{
      success: boolean;
      query: string;
      count: number;
      data: Service[];
    }>(`${this.endpoint}/search`, params);
  }

  /**
   * Filtre les services par type
   */
  getServicesBySpecificType(type: 'B2C' | 'B2B'): Observable<ServiceResponse> {
    return this.getAllServices({ type });
  }

  /**
   * Filtre les services par catégorie
   */
  getServicesByCategory(category: string): Observable<ServiceResponse> {
    return this.getAllServices({ category });
  }

  /**
   * Alias pour getAllServices (pour compatibilité avec les composants)
   */
  getServices(): Observable<Service[]> {
    return new Observable(observer => {
      this.getAllServices().subscribe({
        next: (response) => {
          if (response.success && Array.isArray(response.data)) {
            observer.next(response.data);
          } else {
            observer.next([]);
          }
        },
        error: (error) => observer.error(error),
        complete: () => observer.complete()
      });
    });
  }

  /**
   * Récupère un service par son ID (alias pour getServiceById)
   */
  getService(id: string): Observable<Service> {
    return new Observable(observer => {
      this.getServiceById(id).subscribe({
        next: (response) => {
          if (response.success && !Array.isArray(response.data) && typeof response.data === 'object' && 'id' in response.data) {
            observer.next(response.data as Service);
          } else {
            observer.error(new Error('Service not found'));
          }
        },
        error: (error) => observer.error(error),
        complete: () => observer.complete()
      });
    });
  }
}
