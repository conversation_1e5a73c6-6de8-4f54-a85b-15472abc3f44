{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/contact.service\";\nimport * as i3 from \"@angular/common\";\nfunction ContactComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h4\");\n    i0.ɵɵtext(5, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"<EMAIL>\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 19);\n    i0.ɵɵelement(9, \"i\", 21);\n    i0.ɵɵelementStart(10, \"div\")(11, \"h4\");\n    i0.ɵɵtext(12, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14, \"+33 1 23 45 67 89\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 19);\n    i0.ɵɵelement(16, \"i\", 22);\n    i0.ɵɵelementStart(17, \"div\")(18, \"h4\");\n    i0.ɵɵtext(19, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\");\n    i0.ɵɵtext(21, \"123 Rue du D\\u00E9veloppement\");\n    i0.ɵɵelement(22, \"br\");\n    i0.ɵɵtext(23, \"75001 Paris, France\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ContactComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" Le nom est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_div_18_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"L'email est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_div_18_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Format d'email invalide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, ContactComponent_div_18_span_1_Template, 2, 0, \"span\", 24)(2, ContactComponent_div_18_span_2_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.contactForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.contactForm.get(\"email\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"email\"]);\n  }\n}\nfunction ContactComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" Le sujet est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" Le message est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1, \" Votre message a \\u00E9t\\u00E9 envoy\\u00E9 avec succ\\u00E8s ! Nous vous r\\u00E9pondrons dans les plus brefs d\\u00E9lais. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" Une erreur s'est produite lors de l'envoi. Veuillez r\\u00E9essayer. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ContactComponent {\n  constructor(fb, contactService) {\n    this.fb = fb;\n    this.contactService = contactService;\n    this.isHomePage = false;\n    this.isSubmitting = false;\n    this.showSuccessMessage = false;\n    this.showErrorMessage = false;\n    this.contactForm = this.fb.group({\n      name: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      subject: ['', Validators.required],\n      message: ['', Validators.required]\n    });\n  }\n  ngOnInit() {}\n  onSubmit() {\n    if (this.contactForm.valid) {\n      this.isSubmitting = true;\n      this.showSuccessMessage = false;\n      this.showErrorMessage = false;\n      this.contactService.sendMessage(this.contactForm.value).subscribe({\n        next: () => {\n          this.showSuccessMessage = true;\n          this.contactForm.reset();\n          this.isSubmitting = false;\n        },\n        error: () => {\n          this.showErrorMessage = true;\n          this.isSubmitting = false;\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      Object.keys(this.contactForm.controls).forEach(key => {\n        this.contactForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n  static {\n    this.ɵfac = function ContactComponent_Factory(t) {\n      return new (t || ContactComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ContactService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactComponent,\n      selectors: [[\"app-contact\"]],\n      inputs: {\n        isHomePage: \"isHomePage\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 33,\n      vars: 21,\n      consts: [[1, \"contact-section\"], [1, \"container\"], [1, \"section-description\"], [1, \"contact-content\"], [\"class\", \"contact-info\", 4, \"ngIf\"], [1, \"contact-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\"], [\"for\", \"subject\"], [\"type\", \"text\", \"id\", \"subject\", \"formControlName\", \"subject\"], [\"for\", \"message\"], [\"id\", \"message\", \"formControlName\", \"message\", \"rows\", \"5\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"success-message\", 4, \"ngIf\"], [1, \"contact-info\"], [1, \"info-item\"], [1, \"fas\", \"fa-envelope\"], [1, \"fas\", \"fa-phone\"], [1, \"fas\", \"fa-map-marker-alt\"], [1, \"error-message\"], [4, \"ngIf\"], [1, \"success-message\"]],\n      template: function ContactComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 2);\n          i0.ɵɵtext(5, \" {{ isHomePage ? 'Une question ? N'h\\u00E9sitez pas \\u00E0 nous \\u00E9crire.' : 'Nous sommes l\\u00E0 pour vous accompagner dans votre d\\u00E9marche de d\\u00E9veloppement personnel.' }} \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3);\n          i0.ɵɵtemplate(7, ContactComponent_div_7_Template, 24, 0, \"div\", 4);\n          i0.ɵɵelementStart(8, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function ContactComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"label\", 7);\n          i0.ɵɵtext(11, \"Nom complet *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 8);\n          i0.ɵɵtemplate(13, ContactComponent_div_13_Template, 2, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 6)(15, \"label\", 10);\n          i0.ɵɵtext(16, \"Email *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 11);\n          i0.ɵɵtemplate(18, ContactComponent_div_18_Template, 3, 2, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 6)(20, \"label\", 12);\n          i0.ɵɵtext(21, \"Sujet *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 13);\n          i0.ɵɵtemplate(23, ContactComponent_div_23_Template, 2, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 6)(25, \"label\", 14);\n          i0.ɵɵtext(26, \"Message *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"textarea\", 15);\n          i0.ɵɵtemplate(28, ContactComponent_div_28_Template, 2, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 16);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, ContactComponent_div_31_Template, 2, 0, \"div\", 17)(32, ContactComponent_div_32_Template, 2, 0, \"div\", 9);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_9_0;\n          let tmp_10_0;\n          let tmp_11_0;\n          i0.ɵɵclassProp(\"home-section\", ctx.isHomePage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.isHomePage ? \"Contactez-nous\" : \"Nous Contacter\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isHomePage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.contactForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"error\", ((tmp_4_0 = ctx.contactForm.get(\"name\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.contactForm.get(\"name\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.contactForm.get(\"name\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.contactForm.get(\"name\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"error\", ((tmp_6_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"error\", ((tmp_8_0 = ctx.contactForm.get(\"subject\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.contactForm.get(\"subject\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx.contactForm.get(\"subject\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.contactForm.get(\"subject\")) == null ? null : tmp_9_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"error\", ((tmp_10_0 = ctx.contactForm.get(\"message\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.contactForm.get(\"message\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.contactForm.get(\"message\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.contactForm.get(\"message\")) == null ? null : tmp_11_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.contactForm.invalid || ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Envoi en cours...\" : \"Envoyer le message\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSuccessMessage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showErrorMessage);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\".contact-section[_ngcontent-%COMP%] {\\n  padding: 80px 0;\\n  background: #f8f9fa;\\n}\\n\\n.home-section[_ngcontent-%COMP%] {\\n  padding: 60px 0;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 20px;\\n  color: #2c3e50;\\n  font-size: 2.5rem;\\n}\\n\\n.section-description[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.contact-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 2fr;\\n  gap: 40px;\\n  align-items: start;\\n}\\n\\n@media (max-width: 768px) {\\n  .contact-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.contact-info[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 25px;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #3498db;\\n  margin-right: 15px;\\n  width: 30px;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  color: #2c3e50;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n}\\n\\n.contact-form[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 5px;\\n  color: #2c3e50;\\n  font-weight: 500;\\n}\\n\\ninput[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  font-size: 16px;\\n  transition: border-color 0.3s ease;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus, textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3498db;\\n}\\n\\ninput.error[_ngcontent-%COMP%], textarea.error[_ngcontent-%COMP%] {\\n  border-color: #e74c3c;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 30px;\\n  border: none;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  font-size: 16px;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #2980b9;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  font-size: 14px;\\n  margin-top: 5px;\\n}\\n\\n.success-message[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  background: #d5f4e6;\\n  padding: 15px;\\n  border-radius: 5px;\\n  margin-top: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ContactComponent_div_18_span_1_Template", "ContactComponent_div_18_span_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "ctx_r0", "contactForm", "get", "errors", "tmp_2_0", "ContactComponent", "constructor", "fb", "contactService", "isHomePage", "isSubmitting", "showSuccessMessage", "showErrorMessage", "group", "name", "required", "email", "subject", "message", "ngOnInit", "onSubmit", "valid", "sendMessage", "value", "subscribe", "next", "reset", "error", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ContactService", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ContactComponent_Template", "rf", "ctx", "ContactComponent_div_7_Template", "ɵɵlistener", "ContactComponent_Template_form_ngSubmit_8_listener", "ContactComponent_div_13_Template", "ContactComponent_div_18_Template", "ContactComponent_div_23_Template", "ContactComponent_div_28_Template", "ContactComponent_div_31_Template", "ContactComponent_div_32_Template", "ɵɵclassProp", "ɵɵtextInterpolate", "tmp_4_0", "invalid", "touched", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_9_0", "tmp_10_0", "tmp_11_0", "ɵɵtextInterpolate1", "i3", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\contact\\contact.component.ts"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ContactService } from '../../services/contact.service';\n\n@Component({\n  selector: 'app-contact',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  template: `\n    <section class=\"contact-section\" [class.home-section]=\"isHomePage\">\n      <div class=\"container\">\n        <h2>{{ isHomePage ? 'Contactez-nous' : 'Nous Contacter' }}</h2>\n        <p class=\"section-description\">\n          {{ isHomePage ? 'Une question ? N\\'hésitez pas à nous écrire.' : 'Nous sommes là pour vous accompagner dans votre démarche de développement personnel.' }}\n        </p>\n        \n        <div class=\"contact-content\">\n          <div class=\"contact-info\" *ngIf=\"!isHomePage\">\n            <div class=\"info-item\">\n              <i class=\"fas fa-envelope\"></i>\n              <div>\n                <h4>Email</h4>\n                <p>contact&#64;disconnecttoconnect.fr</p>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <i class=\"fas fa-phone\"></i>\n              <div>\n                <h4>Téléphone</h4>\n                <p>+33 1 23 45 67 89</p>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <i class=\"fas fa-map-marker-alt\"></i>\n              <div>\n                <h4>Adresse</h4>\n                <p>123 Rue du Développement<br>75001 Paris, France</p>\n              </div>\n            </div>\n          </div>\n          \n          <form [formGroup]=\"contactForm\" (ngSubmit)=\"onSubmit()\" class=\"contact-form\">\n            <div class=\"form-group\">\n              <label for=\"name\">Nom complet *</label>\n              <input \n                type=\"text\" \n                id=\"name\" \n                formControlName=\"name\"\n                [class.error]=\"contactForm.get('name')?.invalid && contactForm.get('name')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('name')?.invalid && contactForm.get('name')?.touched\">\n                Le nom est requis\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"email\">Email *</label>\n              <input \n                type=\"email\" \n                id=\"email\" \n                formControlName=\"email\"\n                [class.error]=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\">\n                <span *ngIf=\"contactForm.get('email')?.errors?.['required']\">L'email est requis</span>\n                <span *ngIf=\"contactForm.get('email')?.errors?.['email']\">Format d'email invalide</span>\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"subject\">Sujet *</label>\n              <input \n                type=\"text\" \n                id=\"subject\" \n                formControlName=\"subject\"\n                [class.error]=\"contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched\">\n                Le sujet est requis\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"message\">Message *</label>\n              <textarea \n                id=\"message\" \n                formControlName=\"message\" \n                rows=\"5\"\n                [class.error]=\"contactForm.get('message')?.invalid && contactForm.get('message')?.touched\"\n              ></textarea>\n              <div class=\"error-message\" *ngIf=\"contactForm.get('message')?.invalid && contactForm.get('message')?.touched\">\n                Le message est requis\n              </div>\n            </div>\n            \n            <button \n              type=\"submit\" \n              class=\"btn btn-primary\"\n              [disabled]=\"contactForm.invalid || isSubmitting\"\n            >\n              {{ isSubmitting ? 'Envoi en cours...' : 'Envoyer le message' }}\n            </button>\n            \n            <div class=\"success-message\" *ngIf=\"showSuccessMessage\">\n              Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.\n            </div>\n            \n            <div class=\"error-message\" *ngIf=\"showErrorMessage\">\n              Une erreur s'est produite lors de l'envoi. Veuillez réessayer.\n            </div>\n          </form>\n        </div>\n      </div>\n    </section>\n  `,\n  styles: [`\n    .contact-section {\n      padding: 80px 0;\n      background: #f8f9fa;\n    }\n    \n    .home-section {\n      padding: 60px 0;\n    }\n    \n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n    \n    h2 {\n      text-align: center;\n      margin-bottom: 20px;\n      color: #2c3e50;\n      font-size: 2.5rem;\n    }\n    \n    .section-description {\n      text-align: center;\n      margin-bottom: 40px;\n      color: #666;\n      font-size: 1.1rem;\n    }\n    \n    .contact-content {\n      display: grid;\n      grid-template-columns: 1fr 2fr;\n      gap: 40px;\n      align-items: start;\n    }\n    \n    @media (max-width: 768px) {\n      .contact-content {\n        grid-template-columns: 1fr;\n      }\n    }\n    \n    .contact-info {\n      background: white;\n      padding: 30px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n    }\n    \n    .info-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 25px;\n    }\n    \n    .info-item i {\n      font-size: 1.5rem;\n      color: #3498db;\n      margin-right: 15px;\n      width: 30px;\n    }\n    \n    .info-item h4 {\n      margin: 0 0 5px 0;\n      color: #2c3e50;\n    }\n    \n    .info-item p {\n      margin: 0;\n      color: #666;\n    }\n    \n    .contact-form {\n      background: white;\n      padding: 30px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n    }\n    \n    .form-group {\n      margin-bottom: 20px;\n    }\n    \n    label {\n      display: block;\n      margin-bottom: 5px;\n      color: #2c3e50;\n      font-weight: 500;\n    }\n    \n    input, textarea {\n      width: 100%;\n      padding: 12px;\n      border: 1px solid #ddd;\n      border-radius: 5px;\n      font-size: 16px;\n      transition: border-color 0.3s ease;\n    }\n    \n    input:focus, textarea:focus {\n      outline: none;\n      border-color: #3498db;\n    }\n    \n    input.error, textarea.error {\n      border-color: #e74c3c;\n    }\n    \n    .btn {\n      padding: 12px 30px;\n      border: none;\n      border-radius: 5px;\n      cursor: pointer;\n      font-size: 16px;\n      transition: all 0.3s ease;\n    }\n    \n    .btn-primary {\n      background: #3498db;\n      color: white;\n    }\n    \n    .btn-primary:hover:not(:disabled) {\n      background: #2980b9;\n    }\n    \n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n    \n    .error-message {\n      color: #e74c3c;\n      font-size: 14px;\n      margin-top: 5px;\n    }\n    \n    .success-message {\n      color: #27ae60;\n      background: #d5f4e6;\n      padding: 15px;\n      border-radius: 5px;\n      margin-top: 20px;\n    }\n  `]\n})\nexport class ContactComponent implements OnInit {\n  @Input() isHomePage: boolean = false;\n  \n  contactForm: FormGroup;\n  isSubmitting = false;\n  showSuccessMessage = false;\n  showErrorMessage = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private contactService: ContactService\n  ) {\n    this.contactForm = this.fb.group({\n      name: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      subject: ['', Validators.required],\n      message: ['', Validators.required]\n    });\n  }\n\n  ngOnInit(): void {}\n\n  onSubmit(): void {\n    if (this.contactForm.valid) {\n      this.isSubmitting = true;\n      this.showSuccessMessage = false;\n      this.showErrorMessage = false;\n\n      this.contactService.sendMessage(this.contactForm.value).subscribe({\n        next: () => {\n          this.showSuccessMessage = true;\n          this.contactForm.reset();\n          this.isSubmitting = false;\n        },\n        error: () => {\n          this.showErrorMessage = true;\n          this.isSubmitting = false;\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      Object.keys(this.contactForm.controls).forEach(key => {\n        this.contactForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;;;;;;;IAiB5EC,EADF,CAAAC,cAAA,cAA8C,cACrB;IACrBD,EAAA,CAAAE,SAAA,YAA+B;IAE7BF,EADF,CAAAC,cAAA,UAAK,SACC;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACdJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,qCAAkC;IAEzCH,EAFyC,CAAAI,YAAA,EAAI,EACrC,EACF;IACNJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,YAA4B;IAE1BF,EADF,CAAAC,cAAA,WAAK,UACC;IAAAD,EAAA,CAAAG,MAAA,2BAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClBJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAExBH,EAFwB,CAAAI,YAAA,EAAI,EACpB,EACF;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,SAAA,aAAqC;IAEnCF,EADF,CAAAC,cAAA,WAAK,UACC;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChBJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,qCAAwB;IAAAH,EAAA,CAAAE,SAAA,UAAI;IAAAF,EAAA,CAAAG,MAAA,2BAAmB;IAGxDH,EAHwD,CAAAI,YAAA,EAAI,EAClD,EACF,EACF;;;;;IAWFJ,EAAA,CAAAC,cAAA,cAAwG;IACtGD,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAYJJ,EAAA,CAAAC,cAAA,WAA6D;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACtFJ,EAAA,CAAAC,cAAA,WAA0D;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAF1FJ,EAAA,CAAAC,cAAA,cAA0G;IAExGD,EADA,CAAAK,UAAA,IAAAC,uCAAA,mBAA6D,IAAAC,uCAAA,mBACH;IAC5DP,EAAA,CAAAI,YAAA,EAAM;;;;;;IAFGJ,EAAA,CAAAQ,SAAA,EAAoD;IAApDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,WAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAoD;IACpDd,EAAA,CAAAQ,SAAA,EAAiD;IAAjDR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,WAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAAiD;;;;;IAY1Dd,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAWNJ,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAWRJ,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAG,MAAA,gIACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAENJ,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAG,MAAA,4EACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;AAyJlB,OAAM,MAAOY,gBAAgB;EAQ3BC,YACUC,EAAe,EACfC,cAA8B;IAD9B,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IATf,KAAAC,UAAU,GAAY,KAAK;IAGpC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IAMtB,IAAI,CAACX,WAAW,GAAG,IAAI,CAACM,EAAE,CAACM,KAAK,CAAC;MAC/BC,IAAI,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAAC2B,QAAQ,CAAC;MAC/BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,KAAK,CAAC,CAAC;MACpDC,OAAO,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAAC2B,QAAQ,CAAC;MAClCG,OAAO,EAAE,CAAC,EAAE,EAAE9B,UAAU,CAAC2B,QAAQ;KAClC,CAAC;EACJ;EAEAI,QAAQA,CAAA,GAAU;EAElBC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnB,WAAW,CAACoB,KAAK,EAAE;MAC1B,IAAI,CAACX,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAE7B,IAAI,CAACJ,cAAc,CAACc,WAAW,CAAC,IAAI,CAACrB,WAAW,CAACsB,KAAK,CAAC,CAACC,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACd,kBAAkB,GAAG,IAAI;UAC9B,IAAI,CAACV,WAAW,CAACyB,KAAK,EAAE;UACxB,IAAI,CAAChB,YAAY,GAAG,KAAK;QAC3B,CAAC;QACDiB,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACf,gBAAgB,GAAG,IAAI;UAC5B,IAAI,CAACF,YAAY,GAAG,KAAK;QAC3B;OACD,CAAC;KACH,MAAM;MACL;MACAkB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5B,WAAW,CAAC6B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACnD,IAAI,CAAC/B,WAAW,CAACC,GAAG,CAAC8B,GAAG,CAAC,EAAEC,aAAa,EAAE;MAC5C,CAAC,CAAC;;EAEN;;;uBA7CW5B,gBAAgB,EAAAhB,EAAA,CAAA6C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/C,EAAA,CAAA6C,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAhBjC,gBAAgB;MAAAkC,SAAA;MAAAC,MAAA;QAAA/B,UAAA;MAAA;MAAAgC,UAAA;MAAAC,QAAA,GAAArD,EAAA,CAAAsD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3PrB5D,EAFJ,CAAAC,cAAA,iBAAmE,aAC1C,SACjB;UAAAD,EAAA,CAAAG,MAAA,GAAsD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/DJ,EAAA,CAAAC,cAAA,WAA+B;UAC7BD,EAAA,CAAAG,MAAA,gMACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAEJJ,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAK,UAAA,IAAAyD,+BAAA,kBAA8C;UAwB9C9D,EAAA,CAAAC,cAAA,cAA6E;UAA7CD,EAAA,CAAA+D,UAAA,sBAAAC,mDAAA;YAAA,OAAYH,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC;UAEnD/B,EADF,CAAAC,cAAA,aAAwB,gBACJ;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACvCJ,EAAA,CAAAE,SAAA,gBAKC;UACDF,EAAA,CAAAK,UAAA,KAAA4D,gCAAA,iBAAwG;UAG1GjE,EAAA,CAAAI,YAAA,EAAM;UAGJJ,EADF,CAAAC,cAAA,cAAwB,iBACH;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAClCJ,EAAA,CAAAE,SAAA,iBAKC;UACDF,EAAA,CAAAK,UAAA,KAAA6D,gCAAA,iBAA0G;UAI5GlE,EAAA,CAAAI,YAAA,EAAM;UAGJJ,EADF,CAAAC,cAAA,cAAwB,iBACD;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACpCJ,EAAA,CAAAE,SAAA,iBAKC;UACDF,EAAA,CAAAK,UAAA,KAAA8D,gCAAA,iBAA8G;UAGhHnE,EAAA,CAAAI,YAAA,EAAM;UAGJJ,EADF,CAAAC,cAAA,cAAwB,iBACD;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACtCJ,EAAA,CAAAE,SAAA,oBAKY;UACZF,EAAA,CAAAK,UAAA,KAAA+D,gCAAA,iBAA8G;UAGhHpE,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAMTJ,EAJA,CAAAK,UAAA,KAAAgE,gCAAA,kBAAwD,KAAAC,gCAAA,iBAIJ;UAM5DtE,EAHM,CAAAI,YAAA,EAAO,EACH,EACF,EACE;;;;;;;;;;;UAxGuBJ,EAAA,CAAAuE,WAAA,iBAAAV,GAAA,CAAAzC,UAAA,CAAiC;UAE1DpB,EAAA,CAAAQ,SAAA,GAAsD;UAAtDR,EAAA,CAAAwE,iBAAA,CAAAX,GAAA,CAAAzC,UAAA,uCAAsD;UAM7BpB,EAAA,CAAAQ,SAAA,GAAiB;UAAjBR,EAAA,CAAAS,UAAA,UAAAoD,GAAA,CAAAzC,UAAA,CAAiB;UAwBtCpB,EAAA,CAAAQ,SAAA,EAAyB;UAAzBR,EAAA,CAAAS,UAAA,cAAAoD,GAAA,CAAAjD,WAAA,CAAyB;UAOzBZ,EAAA,CAAAQ,SAAA,GAAoF;UAApFR,EAAA,CAAAuE,WAAA,YAAAE,OAAA,GAAAZ,GAAA,CAAAjD,WAAA,CAAAC,GAAA,2BAAA4D,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAZ,GAAA,CAAAjD,WAAA,CAAAC,GAAA,2BAAA4D,OAAA,CAAAE,OAAA,EAAoF;UAE1D3E,EAAA,CAAAQ,SAAA,EAA0E;UAA1ER,EAAA,CAAAS,UAAA,WAAAmE,OAAA,GAAAf,GAAA,CAAAjD,WAAA,CAAAC,GAAA,2BAAA+D,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAf,GAAA,CAAAjD,WAAA,CAAAC,GAAA,2BAAA+D,OAAA,CAAAD,OAAA,EAA0E;UAWpG3E,EAAA,CAAAQ,SAAA,GAAsF;UAAtFR,EAAA,CAAAuE,WAAA,YAAAM,OAAA,GAAAhB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,4BAAAgE,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAhB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,4BAAAgE,OAAA,CAAAF,OAAA,EAAsF;UAE5D3E,EAAA,CAAAQ,SAAA,EAA4E;UAA5ER,EAAA,CAAAS,UAAA,WAAAqE,OAAA,GAAAjB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,4BAAAiE,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAjB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,4BAAAiE,OAAA,CAAAH,OAAA,EAA4E;UAYtG3E,EAAA,CAAAQ,SAAA,GAA0F;UAA1FR,EAAA,CAAAuE,WAAA,YAAAQ,OAAA,GAAAlB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,8BAAAkE,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAlB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,8BAAAkE,OAAA,CAAAJ,OAAA,EAA0F;UAEhE3E,EAAA,CAAAQ,SAAA,EAAgF;UAAhFR,EAAA,CAAAS,UAAA,WAAAuE,OAAA,GAAAnB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,8BAAAmE,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAnB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,8BAAAmE,OAAA,CAAAL,OAAA,EAAgF;UAW1G3E,EAAA,CAAAQ,SAAA,GAA0F;UAA1FR,EAAA,CAAAuE,WAAA,YAAAU,QAAA,GAAApB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,8BAAAoE,QAAA,CAAAP,OAAA,OAAAO,QAAA,GAAApB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,8BAAAoE,QAAA,CAAAN,OAAA,EAA0F;UAEhE3E,EAAA,CAAAQ,SAAA,EAAgF;UAAhFR,EAAA,CAAAS,UAAA,WAAAyE,QAAA,GAAArB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,8BAAAqE,QAAA,CAAAR,OAAA,OAAAQ,QAAA,GAAArB,GAAA,CAAAjD,WAAA,CAAAC,GAAA,8BAAAqE,QAAA,CAAAP,OAAA,EAAgF;UAQ5G3E,EAAA,CAAAQ,SAAA,EAAgD;UAAhDR,EAAA,CAAAS,UAAA,aAAAoD,GAAA,CAAAjD,WAAA,CAAA8D,OAAA,IAAAb,GAAA,CAAAxC,YAAA,CAAgD;UAEhDrB,EAAA,CAAAQ,SAAA,EACF;UADER,EAAA,CAAAmF,kBAAA,MAAAtB,GAAA,CAAAxC,YAAA,mDACF;UAE8BrB,EAAA,CAAAQ,SAAA,EAAwB;UAAxBR,EAAA,CAAAS,UAAA,SAAAoD,GAAA,CAAAvC,kBAAA,CAAwB;UAI1BtB,EAAA,CAAAQ,SAAA,EAAsB;UAAtBR,EAAA,CAAAS,UAAA,SAAAoD,GAAA,CAAAtC,gBAAA,CAAsB;;;qBApGlD1B,YAAY,EAAAuF,EAAA,CAAAC,IAAA,EAAEvF,mBAAmB,EAAAgD,EAAA,CAAAwC,aAAA,EAAAxC,EAAA,CAAAyC,oBAAA,EAAAzC,EAAA,CAAA0C,eAAA,EAAA1C,EAAA,CAAA2C,oBAAA,EAAA3C,EAAA,CAAA4C,kBAAA,EAAA5C,EAAA,CAAA6C,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}