{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { takeLast } from './takeLast';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { identity } from '../util/identity';\nexport function last(predicate, defaultValue) {\n  const hasDefaultValue = arguments.length >= 2;\n  return source => source.pipe(predicate ? filter((v, i) => predicate(v, i, source)) : identity, takeLast(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new EmptyError()));\n}", "map": {"version": 3, "names": ["EmptyError", "filter", "takeLast", "throwIfEmpty", "defaultIfEmpty", "identity", "last", "predicate", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "v", "i"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Site e-commerce/client/node_modules/rxjs/dist/esm/internal/operators/last.js"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { takeLast } from './takeLast';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { identity } from '../util/identity';\nexport function last(predicate, defaultValue) {\n    const hasDefaultValue = arguments.length >= 2;\n    return (source) => source.pipe(predicate ? filter((v, i) => predicate(v, i, source)) : identity, takeLast(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new EmptyError()));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,SAASC,IAAIA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC1C,MAAMC,eAAe,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC;EAC7C,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACN,SAAS,GAAGN,MAAM,CAAC,CAACa,CAAC,EAAEC,CAAC,KAAKR,SAAS,CAACO,CAAC,EAAEC,CAAC,EAAEH,MAAM,CAAC,CAAC,GAAGP,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEO,eAAe,GAAGL,cAAc,CAACI,YAAY,CAAC,GAAGL,YAAY,CAAC,MAAM,IAAIH,UAAU,CAAC,CAAC,CAAC,CAAC;AACxM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}