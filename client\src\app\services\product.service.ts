import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { Product, ProductResponse, ProductSearchParams, ProductStatistics } from '../models/product.interface';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private readonly endpoint = '/products';

  constructor(private apiService: ApiService) {}

  /**
   * Récupère tous les produits
   */
  getAllProducts(params?: { category?: string; inStock?: boolean }): Observable<ProductResponse> {
    return this.apiService.get<ProductResponse>(this.endpoint, params);
  }

  /**
   * Récupère un produit par son ID
   */
  getProductById(id: string): Observable<ProductResponse> {
    return this.apiService.get<ProductResponse>(`${this.endpoint}/${id}`);
  }

  /**
   * Récupère toutes les catégories de produits
   */
  getCategories(): Observable<{ success: boolean; count: number; data: string[] }> {
    return this.apiService.get<{ success: boolean; count: number; data: string[] }>(`${this.endpoint}/categories`);
  }

  /**
   * Récupère les produits groupés par catégorie
   */
  getProductsByCategory(): Observable<{
    success: boolean;
    data: { [category: string]: Product[] };
    statistics: ProductStatistics;
  }> {
    return this.apiService.get<{
      success: boolean;
      data: { [category: string]: Product[] };
      statistics: ProductStatistics;
    }>(`${this.endpoint}/by-category`);
  }

  /**
   * Recherche des produits
   */
  searchProducts(params: ProductSearchParams): Observable<{
    success: boolean;
    query: string;
    count: number;
    data: Product[];
  }> {
    return this.apiService.get<{
      success: boolean;
      query: string;
      count: number;
      data: Product[];
    }>(`${this.endpoint}/search`, params);
  }

  /**
   * Filtre les produits par catégorie
   */
  getProductsBySpecificCategory(category: string): Observable<ProductResponse> {
    return this.getAllProducts({ category });
  }

  /**
   * Récupère les produits en stock uniquement
   */
  getAvailableProducts(): Observable<ProductResponse> {
    return this.getAllProducts({ inStock: true });
  }

  /**
   * Alias pour getAllProducts (pour compatibilité avec les composants)
   */
  getProducts(): Observable<Product[]> {
    return new Observable(observer => {
      this.getAllProducts().subscribe({
        next: (response) => {
          if (response.success && Array.isArray(response.data)) {
            observer.next(response.data);
          } else {
            observer.next([]);
          }
        },
        error: (error) => observer.error(error),
        complete: () => observer.complete()
      });
    });
  }

  /**
   * Récupère un produit par son ID (alias pour getProductById)
   */
  getProduct(id: string): Observable<Product> {
    return new Observable(observer => {
      this.getProductById(id).subscribe({
        next: (response) => {
          if (response.success && !Array.isArray(response.data)) {
            observer.next(response.data);
          } else {
            observer.error(new Error('Product not found'));
          }
        },
        error: (error) => observer.error(error),
        complete: () => observer.complete()
      });
    });
  }
}
