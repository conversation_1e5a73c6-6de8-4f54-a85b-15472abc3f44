{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { importProvidersFrom } from '@angular/core';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { routes } from './app/app.routes';\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptorsFromDi()), importProvidersFrom(BrowserAnimationsModule)]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "provideRouter", "provideHttpClient", "withInterceptorsFromDi", "importProvidersFrom", "BrowserAnimationsModule", "routes", "providers", "catch", "err", "console", "error"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { importProvidersFrom } from '@angular/core';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { routes } from './app/app.routes';\n\nbootstrapApplication(AppComponent, {\n  providers: [\n    provideRouter(routes),\n    provideHttpClient(withInterceptorsFromDi()),\n    importProvidersFrom(BrowserAnimationsModule)\n  ]\n}).catch(err => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAChF,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,MAAM,QAAQ,kBAAkB;AAEzCP,oBAAoB,CAACC,YAAY,EAAE;EACjCO,SAAS,EAAE,CACTN,aAAa,CAACK,MAAM,CAAC,EACrBJ,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,EAC3CC,mBAAmB,CAACC,uBAAuB,CAAC;CAE/C,CAAC,CAACG,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}