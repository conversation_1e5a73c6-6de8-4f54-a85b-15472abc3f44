{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nlet ContactComponent = class ContactComponent {\n  constructor(fb, contactService) {\n    this.fb = fb;\n    this.contactService = contactService;\n    this.isHomePage = false;\n    this.isSubmitting = false;\n    this.showSuccessMessage = false;\n    this.showErrorMessage = false;\n    this.contactForm = this.fb.group({\n      name: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      subject: ['', Validators.required],\n      message: ['', Validators.required]\n    });\n  }\n  ngOnInit() {}\n  onSubmit() {\n    if (this.contactForm.valid) {\n      this.isSubmitting = true;\n      this.showSuccessMessage = false;\n      this.showErrorMessage = false;\n      this.contactService.sendMessage(this.contactForm.value).subscribe({\n        next: () => {\n          this.showSuccessMessage = true;\n          this.contactForm.reset();\n          this.isSubmitting = false;\n        },\n        error: () => {\n          this.showErrorMessage = true;\n          this.isSubmitting = false;\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      Object.keys(this.contactForm.controls).forEach(key => {\n        this.contactForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n};\n__decorate([Input()], ContactComponent.prototype, \"isHomePage\", void 0);\nContactComponent = __decorate([Component({\n  selector: 'app-contact',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  template: `\n    <section class=\"contact-section\" [class.home-section]=\"isHomePage\">\n      <div class=\"container\">\n        <h2>{{ isHomePage ? 'Contactez-nous' : 'Nous Contacter' }}</h2>\n        <p class=\"section-description\">\n          {{ isHomePage ? 'Une question ? N\\'hésitez pas à nous écrire.' : 'Nous sommes là pour vous accompagner dans votre démarche de développement personnel.' }}\n        </p>\n        \n        <div class=\"contact-content\">\n          <div class=\"contact-info\" *ngIf=\"!isHomePage\">\n            <div class=\"info-item\">\n              <i class=\"fas fa-envelope\"></i>\n              <div>\n                <h4>Email</h4>\n                <p><EMAIL></p>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <i class=\"fas fa-phone\"></i>\n              <div>\n                <h4>Téléphone</h4>\n                <p>+33 1 23 45 67 89</p>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <i class=\"fas fa-map-marker-alt\"></i>\n              <div>\n                <h4>Adresse</h4>\n                <p>123 Rue du Développement<br>75001 Paris, France</p>\n              </div>\n            </div>\n          </div>\n          \n          <form [formGroup]=\"contactForm\" (ngSubmit)=\"onSubmit()\" class=\"contact-form\">\n            <div class=\"form-group\">\n              <label for=\"name\">Nom complet *</label>\n              <input \n                type=\"text\" \n                id=\"name\" \n                formControlName=\"name\"\n                [class.error]=\"contactForm.get('name')?.invalid && contactForm.get('name')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('name')?.invalid && contactForm.get('name')?.touched\">\n                Le nom est requis\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"email\">Email *</label>\n              <input \n                type=\"email\" \n                id=\"email\" \n                formControlName=\"email\"\n                [class.error]=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\">\n                <span *ngIf=\"contactForm.get('email')?.errors?.['required']\">L'email est requis</span>\n                <span *ngIf=\"contactForm.get('email')?.errors?.['email']\">Format d'email invalide</span>\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"subject\">Sujet *</label>\n              <input \n                type=\"text\" \n                id=\"subject\" \n                formControlName=\"subject\"\n                [class.error]=\"contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched\">\n                Le sujet est requis\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"message\">Message *</label>\n              <textarea \n                id=\"message\" \n                formControlName=\"message\" \n                rows=\"5\"\n                [class.error]=\"contactForm.get('message')?.invalid && contactForm.get('message')?.touched\"\n              ></textarea>\n              <div class=\"error-message\" *ngIf=\"contactForm.get('message')?.invalid && contactForm.get('message')?.touched\">\n                Le message est requis\n              </div>\n            </div>\n            \n            <button \n              type=\"submit\" \n              class=\"btn btn-primary\"\n              [disabled]=\"contactForm.invalid || isSubmitting\"\n            >\n              {{ isSubmitting ? 'Envoi en cours...' : 'Envoyer le message' }}\n            </button>\n            \n            <div class=\"success-message\" *ngIf=\"showSuccessMessage\">\n              Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.\n            </div>\n            \n            <div class=\"error-message\" *ngIf=\"showErrorMessage\">\n              Une erreur s'est produite lors de l'envoi. Veuillez réessayer.\n            </div>\n          </form>\n        </div>\n      </div>\n    </section>\n  `,\n  styles: [`\n    .contact-section {\n      padding: 80px 0;\n      background: #f8f9fa;\n    }\n    \n    .home-section {\n      padding: 60px 0;\n    }\n    \n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n    \n    h2 {\n      text-align: center;\n      margin-bottom: 20px;\n      color: #2c3e50;\n      font-size: 2.5rem;\n    }\n    \n    .section-description {\n      text-align: center;\n      margin-bottom: 40px;\n      color: #666;\n      font-size: 1.1rem;\n    }\n    \n    .contact-content {\n      display: grid;\n      grid-template-columns: 1fr 2fr;\n      gap: 40px;\n      align-items: start;\n    }\n    \n    @media (max-width: 768px) {\n      .contact-content {\n        grid-template-columns: 1fr;\n      }\n    }\n    \n    .contact-info {\n      background: white;\n      padding: 30px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n    }\n    \n    .info-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 25px;\n    }\n    \n    .info-item i {\n      font-size: 1.5rem;\n      color: #3498db;\n      margin-right: 15px;\n      width: 30px;\n    }\n    \n    .info-item h4 {\n      margin: 0 0 5px 0;\n      color: #2c3e50;\n    }\n    \n    .info-item p {\n      margin: 0;\n      color: #666;\n    }\n    \n    .contact-form {\n      background: white;\n      padding: 30px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n    }\n    \n    .form-group {\n      margin-bottom: 20px;\n    }\n    \n    label {\n      display: block;\n      margin-bottom: 5px;\n      color: #2c3e50;\n      font-weight: 500;\n    }\n    \n    input, textarea {\n      width: 100%;\n      padding: 12px;\n      border: 1px solid #ddd;\n      border-radius: 5px;\n      font-size: 16px;\n      transition: border-color 0.3s ease;\n    }\n    \n    input:focus, textarea:focus {\n      outline: none;\n      border-color: #3498db;\n    }\n    \n    input.error, textarea.error {\n      border-color: #e74c3c;\n    }\n    \n    .btn {\n      padding: 12px 30px;\n      border: none;\n      border-radius: 5px;\n      cursor: pointer;\n      font-size: 16px;\n      transition: all 0.3s ease;\n    }\n    \n    .btn-primary {\n      background: #3498db;\n      color: white;\n    }\n    \n    .btn-primary:hover:not(:disabled) {\n      background: #2980b9;\n    }\n    \n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n    \n    .error-message {\n      color: #e74c3c;\n      font-size: 14px;\n      margin-top: 5px;\n    }\n    \n    .success-message {\n      color: #27ae60;\n      background: #d5f4e6;\n      padding: 15px;\n      border-radius: 5px;\n      margin-top: 20px;\n    }\n  `]\n})], ContactComponent);\nexport { ContactComponent };", "map": {"version": 3, "names": ["Component", "Input", "CommonModule", "ReactiveFormsModule", "Validators", "ContactComponent", "constructor", "fb", "contactService", "isHomePage", "isSubmitting", "showSuccessMessage", "showErrorMessage", "contactForm", "group", "name", "required", "email", "subject", "message", "ngOnInit", "onSubmit", "valid", "sendMessage", "value", "subscribe", "next", "reset", "error", "Object", "keys", "controls", "for<PERSON>ach", "key", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "__decorate", "selector", "standalone", "imports", "template", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\contact\\contact.component.ts"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ContactService } from '../../services/contact.service';\n\n@Component({\n  selector: 'app-contact',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  template: `\n    <section class=\"contact-section\" [class.home-section]=\"isHomePage\">\n      <div class=\"container\">\n        <h2>{{ isHomePage ? 'Contactez-nous' : 'Nous Contacter' }}</h2>\n        <p class=\"section-description\">\n          {{ isHomePage ? 'Une question ? N\\'hésitez pas à nous écrire.' : 'Nous sommes là pour vous accompagner dans votre démarche de développement personnel.' }}\n        </p>\n        \n        <div class=\"contact-content\">\n          <div class=\"contact-info\" *ngIf=\"!isHomePage\">\n            <div class=\"info-item\">\n              <i class=\"fas fa-envelope\"></i>\n              <div>\n                <h4>Email</h4>\n                <p><EMAIL></p>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <i class=\"fas fa-phone\"></i>\n              <div>\n                <h4>Téléphone</h4>\n                <p>+33 1 23 45 67 89</p>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <i class=\"fas fa-map-marker-alt\"></i>\n              <div>\n                <h4>Adresse</h4>\n                <p>123 Rue du Développement<br>75001 Paris, France</p>\n              </div>\n            </div>\n          </div>\n          \n          <form [formGroup]=\"contactForm\" (ngSubmit)=\"onSubmit()\" class=\"contact-form\">\n            <div class=\"form-group\">\n              <label for=\"name\">Nom complet *</label>\n              <input \n                type=\"text\" \n                id=\"name\" \n                formControlName=\"name\"\n                [class.error]=\"contactForm.get('name')?.invalid && contactForm.get('name')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('name')?.invalid && contactForm.get('name')?.touched\">\n                Le nom est requis\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"email\">Email *</label>\n              <input \n                type=\"email\" \n                id=\"email\" \n                formControlName=\"email\"\n                [class.error]=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\">\n                <span *ngIf=\"contactForm.get('email')?.errors?.['required']\">L'email est requis</span>\n                <span *ngIf=\"contactForm.get('email')?.errors?.['email']\">Format d'email invalide</span>\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"subject\">Sujet *</label>\n              <input \n                type=\"text\" \n                id=\"subject\" \n                formControlName=\"subject\"\n                [class.error]=\"contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched\"\n              >\n              <div class=\"error-message\" *ngIf=\"contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched\">\n                Le sujet est requis\n              </div>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"message\">Message *</label>\n              <textarea \n                id=\"message\" \n                formControlName=\"message\" \n                rows=\"5\"\n                [class.error]=\"contactForm.get('message')?.invalid && contactForm.get('message')?.touched\"\n              ></textarea>\n              <div class=\"error-message\" *ngIf=\"contactForm.get('message')?.invalid && contactForm.get('message')?.touched\">\n                Le message est requis\n              </div>\n            </div>\n            \n            <button \n              type=\"submit\" \n              class=\"btn btn-primary\"\n              [disabled]=\"contactForm.invalid || isSubmitting\"\n            >\n              {{ isSubmitting ? 'Envoi en cours...' : 'Envoyer le message' }}\n            </button>\n            \n            <div class=\"success-message\" *ngIf=\"showSuccessMessage\">\n              Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.\n            </div>\n            \n            <div class=\"error-message\" *ngIf=\"showErrorMessage\">\n              Une erreur s'est produite lors de l'envoi. Veuillez réessayer.\n            </div>\n          </form>\n        </div>\n      </div>\n    </section>\n  `,\n  styles: [`\n    .contact-section {\n      padding: 80px 0;\n      background: #f8f9fa;\n    }\n    \n    .home-section {\n      padding: 60px 0;\n    }\n    \n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n    \n    h2 {\n      text-align: center;\n      margin-bottom: 20px;\n      color: #2c3e50;\n      font-size: 2.5rem;\n    }\n    \n    .section-description {\n      text-align: center;\n      margin-bottom: 40px;\n      color: #666;\n      font-size: 1.1rem;\n    }\n    \n    .contact-content {\n      display: grid;\n      grid-template-columns: 1fr 2fr;\n      gap: 40px;\n      align-items: start;\n    }\n    \n    @media (max-width: 768px) {\n      .contact-content {\n        grid-template-columns: 1fr;\n      }\n    }\n    \n    .contact-info {\n      background: white;\n      padding: 30px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n    }\n    \n    .info-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 25px;\n    }\n    \n    .info-item i {\n      font-size: 1.5rem;\n      color: #3498db;\n      margin-right: 15px;\n      width: 30px;\n    }\n    \n    .info-item h4 {\n      margin: 0 0 5px 0;\n      color: #2c3e50;\n    }\n    \n    .info-item p {\n      margin: 0;\n      color: #666;\n    }\n    \n    .contact-form {\n      background: white;\n      padding: 30px;\n      border-radius: 10px;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n    }\n    \n    .form-group {\n      margin-bottom: 20px;\n    }\n    \n    label {\n      display: block;\n      margin-bottom: 5px;\n      color: #2c3e50;\n      font-weight: 500;\n    }\n    \n    input, textarea {\n      width: 100%;\n      padding: 12px;\n      border: 1px solid #ddd;\n      border-radius: 5px;\n      font-size: 16px;\n      transition: border-color 0.3s ease;\n    }\n    \n    input:focus, textarea:focus {\n      outline: none;\n      border-color: #3498db;\n    }\n    \n    input.error, textarea.error {\n      border-color: #e74c3c;\n    }\n    \n    .btn {\n      padding: 12px 30px;\n      border: none;\n      border-radius: 5px;\n      cursor: pointer;\n      font-size: 16px;\n      transition: all 0.3s ease;\n    }\n    \n    .btn-primary {\n      background: #3498db;\n      color: white;\n    }\n    \n    .btn-primary:hover:not(:disabled) {\n      background: #2980b9;\n    }\n    \n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n    \n    .error-message {\n      color: #e74c3c;\n      font-size: 14px;\n      margin-top: 5px;\n    }\n    \n    .success-message {\n      color: #27ae60;\n      background: #d5f4e6;\n      padding: 15px;\n      border-radius: 5px;\n      margin-top: 20px;\n    }\n  `]\n})\nexport class ContactComponent implements OnInit {\n  @Input() isHomePage: boolean = false;\n  \n  contactForm: FormGroup;\n  isSubmitting = false;\n  showSuccessMessage = false;\n  showErrorMessage = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private contactService: ContactService\n  ) {\n    this.contactForm = this.fb.group({\n      name: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      subject: ['', Validators.required],\n      message: ['', Validators.required]\n    });\n  }\n\n  ngOnInit(): void {}\n\n  onSubmit(): void {\n    if (this.contactForm.valid) {\n      this.isSubmitting = true;\n      this.showSuccessMessage = false;\n      this.showErrorMessage = false;\n\n      this.contactService.sendMessage(this.contactForm.value).subscribe({\n        next: () => {\n          this.showSuccessMessage = true;\n          this.contactForm.reset();\n          this.isSubmitting = false;\n        },\n        error: () => {\n          this.showErrorMessage = true;\n          this.isSubmitting = false;\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      Object.keys(this.contactForm.controls).forEach(key => {\n        this.contactForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,QAAgB,eAAe;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AAqQjF,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAQ3BC,YACUC,EAAe,EACfC,cAA8B;IAD9B,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IATf,KAAAC,UAAU,GAAY,KAAK;IAGpC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IAMtB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC/BC,IAAI,EAAE,CAAC,EAAE,EAAEX,UAAU,CAACY,QAAQ,CAAC;MAC/BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACb,UAAU,CAACY,QAAQ,EAAEZ,UAAU,CAACa,KAAK,CAAC,CAAC;MACpDC,OAAO,EAAE,CAAC,EAAE,EAAEd,UAAU,CAACY,QAAQ,CAAC;MAClCG,OAAO,EAAE,CAAC,EAAE,EAAEf,UAAU,CAACY,QAAQ;KAClC,CAAC;EACJ;EAEAI,QAAQA,CAAA,GAAU;EAElBC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACR,WAAW,CAACS,KAAK,EAAE;MAC1B,IAAI,CAACZ,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAE7B,IAAI,CAACJ,cAAc,CAACe,WAAW,CAAC,IAAI,CAACV,WAAW,CAACW,KAAK,CAAC,CAACC,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACf,kBAAkB,GAAG,IAAI;UAC9B,IAAI,CAACE,WAAW,CAACc,KAAK,EAAE;UACxB,IAAI,CAACjB,YAAY,GAAG,KAAK;QAC3B,CAAC;QACDkB,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAChB,gBAAgB,GAAG,IAAI;UAC5B,IAAI,CAACF,YAAY,GAAG,KAAK;QAC3B;OACD,CAAC;KACH,MAAM;MACL;MACAmB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjB,WAAW,CAACkB,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACnD,IAAI,CAACpB,WAAW,CAACqB,GAAG,CAACD,GAAG,CAAC,EAAEE,aAAa,EAAE;MAC5C,CAAC,CAAC;;EAEN;CACD;AA7CUC,UAAA,EAARnC,KAAK,EAAE,C,mDAA6B;AAD1BI,gBAAgB,GAAA+B,UAAA,EAlQ5BpC,SAAS,CAAC;EACTqC,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACrC,YAAY,EAAEC,mBAAmB,CAAC;EAC5CqC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0GT;EACDC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiJR;CACF,CAAC,C,EACWpC,gBAAgB,CA8C5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}