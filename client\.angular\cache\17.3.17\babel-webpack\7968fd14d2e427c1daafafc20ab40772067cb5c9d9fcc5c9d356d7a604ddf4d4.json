{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/product.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = a0 => [\"/products\", a0];\nfunction ProductsComponent_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 6);\n    i0.ɵɵtext(1, \" D\\u00E9couvrez notre collection compl\\u00E8te de cartes de d\\u00E9veloppement personnel \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductsComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8);\n    i0.ɵɵelement(2, \"img\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 10)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 11);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 12);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 13);\n    i0.ɵɵtext(11, \" Voir les d\\u00E9tails \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r1.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r1.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r1.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", product_r1.price, \"\\u20AC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(6, _c0, product_r1.id));\n  }\n}\nfunction ProductsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"button\", 15);\n    i0.ɵɵtext(2, \" Voir tous nos produits \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ProductsComponent {\n  constructor(productService) {\n    this.productService = productService;\n    this.isHomePage = false;\n    this.products = [];\n    this.displayedProducts = [];\n  }\n  ngOnInit() {\n    this.loadProducts();\n  }\n  loadProducts() {\n    this.productService.getProducts().subscribe({\n      next: products => {\n        this.products = products;\n        this.displayedProducts = this.isHomePage ? products.slice(0, 3) : products;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des produits:', error);\n        // Fallback avec des données de démonstration\n        this.products = this.getDemoProducts();\n        this.displayedProducts = this.isHomePage ? this.products.slice(0, 3) : this.products;\n      }\n    });\n  }\n  getDemoProducts() {\n    return [{\n      id: '1',\n      name: 'Cartes Confiance en Soi',\n      description: 'Un jeu de cartes pour développer votre confiance et votre estime de soi.',\n      price: 29.99,\n      image: '/assets/images/cards-confidence.jpg',\n      category: 'Développement Personnel',\n      inStock: true\n    }, {\n      id: '2',\n      name: 'Cartes Méditation',\n      description: 'Découvrez différentes techniques de méditation avec ces cartes guidées.',\n      price: 24.99,\n      image: '/assets/images/cards-meditation.jpg',\n      category: 'Bien-être',\n      inStock: true\n    }, {\n      id: '3',\n      name: 'Cartes Gratitude',\n      description: 'Cultivez la gratitude au quotidien avec ces exercices pratiques.',\n      price: 19.99,\n      image: '/assets/images/cards-gratitude.jpg',\n      category: 'Développement Personnel',\n      inStock: true\n    }];\n  }\n  static {\n    this.ɵfac = function ProductsComponent_Factory(t) {\n      return new (t || ProductsComponent)(i0.ɵɵdirectiveInject(i1.ProductService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProductsComponent,\n      selectors: [[\"app-products\"]],\n      inputs: {\n        isHomePage: \"isHomePage\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 6,\n      consts: [[1, \"products-section\"], [1, \"container\"], [\"class\", \"section-description\", 4, \"ngIf\"], [1, \"products-grid\"], [\"class\", \"product-card\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"section-description\"], [1, \"product-card\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [1, \"product-content\"], [1, \"product-description\"], [1, \"product-price\"], [1, \"btn\", \"btn-primary\", 3, \"routerLink\"], [1, \"text-center\"], [\"routerLink\", \"/products\", 1, \"btn\", \"btn-outline\"]],\n      template: function ProductsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ProductsComponent_p_4_Template, 2, 0, \"p\", 2);\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵtemplate(6, ProductsComponent_div_6_Template, 12, 8, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, ProductsComponent_div_7_Template, 3, 0, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"home-section\", ctx.isHomePage);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.isHomePage ? \"Nos Cartes de D\\u00E9veloppement Personnel\" : \"Toutes nos Cartes\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isHomePage);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.displayedProducts);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isHomePage && ctx.products.length > 3);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, i3.RouterLink],\n      styles: [\".products-section[_ngcontent-%COMP%] {\\n  padding: 80px 0;\\n  background: #f8f9fa;\\n}\\n\\n.home-section[_ngcontent-%COMP%] {\\n  padding: 60px 0;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 20px;\\n  color: #2c3e50;\\n  font-size: 2.5rem;\\n}\\n\\n.section-description[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 30px;\\n  margin-bottom: 40px;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.3s ease;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  height: 200px;\\n  overflow: hidden;\\n}\\n\\n.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.product-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.product-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  color: #2c3e50;\\n}\\n\\n.product-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 15px;\\n  line-height: 1.6;\\n}\\n\\n.product-price[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  color: #e74c3c;\\n  margin-bottom: 15px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  text-decoration: none;\\n  display: inline-block;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #2980b9;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #3498db;\\n  border: 2px solid #3498db;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%]:hover {\\n  background: #3498db;\\n  color: white;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "product_r1", "image", "ɵɵsanitizeUrl", "name", "ɵɵtextInterpolate", "description", "ɵɵtextInterpolate1", "price", "ɵɵpureFunction1", "_c0", "id", "ProductsComponent", "constructor", "productService", "isHomePage", "products", "displayedProducts", "ngOnInit", "loadProducts", "getProducts", "subscribe", "next", "slice", "error", "console", "getDemoProducts", "category", "inStock", "ɵɵdirectiveInject", "i1", "ProductService", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProductsComponent_Template", "rf", "ctx", "ɵɵtemplate", "ProductsComponent_p_4_Template", "ProductsComponent_div_6_Template", "ProductsComponent_div_7_Template", "ɵɵclassProp", "length", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\components\\products\\products.component.ts"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ProductService } from '../../services/product.service';\nimport { Product } from '../../models/product.interface';\n\n@Component({\n  selector: 'app-products',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <section class=\"products-section\" [class.home-section]=\"isHomePage\">\n      <div class=\"container\">\n        <h2>{{ isHomePage ? 'Nos Cartes de Développement Personnel' : 'Toutes nos Cartes' }}</h2>\n        <p class=\"section-description\" *ngIf=\"!isHomePage\">\n          Découvrez notre collection complète de cartes de développement personnel\n        </p>\n        \n        <div class=\"products-grid\">\n          <div class=\"product-card\" *ngFor=\"let product of displayedProducts\">\n            <div class=\"product-image\">\n              <img [src]=\"product.image\" [alt]=\"product.name\" />\n            </div>\n            <div class=\"product-content\">\n              <h3>{{ product.name }}</h3>\n              <p class=\"product-description\">{{ product.description }}</p>\n              <div class=\"product-price\">{{ product.price }}€</div>\n              <button \n                class=\"btn btn-primary\"\n                [routerLink]=\"['/products', product.id]\"\n              >\n                Voir les détails\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"text-center\" *ngIf=\"isHomePage && products.length > 3\">\n          <button class=\"btn btn-outline\" routerLink=\"/products\">\n            Voir tous nos produits\n          </button>\n        </div>\n      </div>\n    </section>\n  `,\n  styles: [`\n    .products-section {\n      padding: 80px 0;\n      background: #f8f9fa;\n    }\n    \n    .home-section {\n      padding: 60px 0;\n    }\n    \n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n    \n    h2 {\n      text-align: center;\n      margin-bottom: 20px;\n      color: #2c3e50;\n      font-size: 2.5rem;\n    }\n    \n    .section-description {\n      text-align: center;\n      margin-bottom: 40px;\n      color: #666;\n      font-size: 1.1rem;\n    }\n    \n    .products-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 30px;\n      margin-bottom: 40px;\n    }\n    \n    .product-card {\n      background: white;\n      border-radius: 10px;\n      overflow: hidden;\n      box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n      transition: transform 0.3s ease;\n    }\n    \n    .product-card:hover {\n      transform: translateY(-5px);\n    }\n    \n    .product-image {\n      height: 200px;\n      overflow: hidden;\n    }\n    \n    .product-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n    \n    .product-content {\n      padding: 20px;\n    }\n    \n    .product-content h3 {\n      margin-bottom: 10px;\n      color: #2c3e50;\n    }\n    \n    .product-description {\n      color: #666;\n      margin-bottom: 15px;\n      line-height: 1.6;\n    }\n    \n    .product-price {\n      font-size: 1.5rem;\n      font-weight: bold;\n      color: #e74c3c;\n      margin-bottom: 15px;\n    }\n    \n    .btn {\n      padding: 10px 20px;\n      border: none;\n      border-radius: 5px;\n      cursor: pointer;\n      text-decoration: none;\n      display: inline-block;\n      text-align: center;\n      transition: all 0.3s ease;\n    }\n    \n    .btn-primary {\n      background: #3498db;\n      color: white;\n    }\n    \n    .btn-primary:hover {\n      background: #2980b9;\n    }\n    \n    .btn-outline {\n      background: transparent;\n      color: #3498db;\n      border: 2px solid #3498db;\n    }\n    \n    .btn-outline:hover {\n      background: #3498db;\n      color: white;\n    }\n    \n    .text-center {\n      text-align: center;\n    }\n  `]\n})\nexport class ProductsComponent implements OnInit {\n  @Input() isHomePage: boolean = false;\n  \n  products: Product[] = [];\n  displayedProducts: Product[] = [];\n\n  constructor(private productService: ProductService) {}\n\n  ngOnInit(): void {\n    this.loadProducts();\n  }\n\n  private loadProducts(): void {\n    this.productService.getProducts().subscribe({\n      next: (products) => {\n        this.products = products;\n        this.displayedProducts = this.isHomePage \n          ? products.slice(0, 3) \n          : products;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des produits:', error);\n        // Fallback avec des données de démonstration\n        this.products = this.getDemoProducts();\n        this.displayedProducts = this.isHomePage \n          ? this.products.slice(0, 3) \n          : this.products;\n      }\n    });\n  }\n\n  private getDemoProducts(): Product[] {\n    return [\n      {\n        id: '1',\n        name: 'Cartes Confiance en Soi',\n        description: 'Un jeu de cartes pour développer votre confiance et votre estime de soi.',\n        price: 29.99,\n        image: '/assets/images/cards-confidence.jpg',\n        category: 'Développement Personnel',\n        inStock: true\n      },\n      {\n        id: '2',\n        name: 'Cartes Méditation',\n        description: 'Découvrez différentes techniques de méditation avec ces cartes guidées.',\n        price: 24.99,\n        image: '/assets/images/cards-meditation.jpg',\n        category: 'Bien-être',\n        inStock: true\n      },\n      {\n        id: '3',\n        name: 'Cartes Gratitude',\n        description: 'Cultivez la gratitude au quotidien avec ces exercices pratiques.',\n        price: 19.99,\n        image: '/assets/images/cards-gratitude.jpg',\n        category: 'Développement Personnel',\n        inStock: true\n      }\n    ];\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;;IAYtCC,EAAA,CAAAC,cAAA,WAAmD;IACjDD,EAAA,CAAAE,MAAA,gGACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAIAH,EADF,CAAAC,cAAA,aAAoE,aACvC;IACzBD,EAAA,CAAAI,SAAA,aAAkD;IACpDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAE,MAAA,+BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAbGH,EAAA,CAAAK,SAAA,GAAqB;IAACL,EAAtB,CAAAM,UAAA,QAAAC,UAAA,CAAAC,KAAA,EAAAR,EAAA,CAAAS,aAAA,CAAqB,QAAAF,UAAA,CAAAG,IAAA,CAAqB;IAG3CV,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAW,iBAAA,CAAAJ,UAAA,CAAAG,IAAA,CAAkB;IACSV,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAW,iBAAA,CAAAJ,UAAA,CAAAK,WAAA,CAAyB;IAC7BZ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAa,kBAAA,KAAAN,UAAA,CAAAO,KAAA,WAAoB;IAG7Cd,EAAA,CAAAK,SAAA,EAAwC;IAAxCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAT,UAAA,CAAAU,EAAA,EAAwC;;;;;IAS9CjB,EADF,CAAAC,cAAA,cAAmE,iBACV;IACrDD,EAAA,CAAAE,MAAA,+BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;AA0Hd,OAAM,MAAOe,iBAAiB;EAM5BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IALzB,KAAAC,UAAU,GAAY,KAAK;IAEpC,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,iBAAiB,GAAc,EAAE;EAEoB;EAErDC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAACL,cAAc,CAACM,WAAW,EAAE,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGN,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACF,UAAU,GACpCC,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACpBP,QAAQ;MACd,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D;QACA,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACU,eAAe,EAAE;QACtC,IAAI,CAACT,iBAAiB,GAAG,IAAI,CAACF,UAAU,GACpC,IAAI,CAACC,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACzB,IAAI,CAACP,QAAQ;MACnB;KACD,CAAC;EACJ;EAEQU,eAAeA,CAAA;IACrB,OAAO,CACL;MACEf,EAAE,EAAE,GAAG;MACPP,IAAI,EAAE,yBAAyB;MAC/BE,WAAW,EAAE,0EAA0E;MACvFE,KAAK,EAAE,KAAK;MACZN,KAAK,EAAE,qCAAqC;MAC5CyB,QAAQ,EAAE,yBAAyB;MACnCC,OAAO,EAAE;KACV,EACD;MACEjB,EAAE,EAAE,GAAG;MACPP,IAAI,EAAE,mBAAmB;MACzBE,WAAW,EAAE,yEAAyE;MACtFE,KAAK,EAAE,KAAK;MACZN,KAAK,EAAE,qCAAqC;MAC5CyB,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE;KACV,EACD;MACEjB,EAAE,EAAE,GAAG;MACPP,IAAI,EAAE,kBAAkB;MACxBE,WAAW,EAAE,kEAAkE;MAC/EE,KAAK,EAAE,KAAK;MACZN,KAAK,EAAE,oCAAoC;MAC3CyB,QAAQ,EAAE,yBAAyB;MACnCC,OAAO,EAAE;KACV,CACF;EACH;;;uBA7DWhB,iBAAiB,EAAAlB,EAAA,CAAAmC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAjBnB,iBAAiB;MAAAoB,SAAA;MAAAC,MAAA;QAAAlB,UAAA;MAAA;MAAAmB,UAAA;MAAAC,QAAA,GAAAzC,EAAA,CAAA0C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtJtBhD,EAFJ,CAAAC,cAAA,iBAAoE,aAC3C,SACjB;UAAAD,EAAA,CAAAE,MAAA,GAAgF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzFH,EAAA,CAAAkD,UAAA,IAAAC,8BAAA,eAAmD;UAInDnD,EAAA,CAAAC,cAAA,aAA2B;UACzBD,EAAA,CAAAkD,UAAA,IAAAE,gCAAA,kBAAoE;UAgBtEpD,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAkD,UAAA,IAAAG,gCAAA,iBAAmE;UAMvErD,EADE,CAAAG,YAAA,EAAM,EACE;;;UAhCwBH,EAAA,CAAAsD,WAAA,iBAAAL,GAAA,CAAA5B,UAAA,CAAiC;UAE3DrB,EAAA,CAAAK,SAAA,GAAgF;UAAhFL,EAAA,CAAAW,iBAAA,CAAAsC,GAAA,CAAA5B,UAAA,sEAAgF;UACpDrB,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAM,UAAA,UAAA2C,GAAA,CAAA5B,UAAA,CAAiB;UAKDrB,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAM,UAAA,YAAA2C,GAAA,CAAA1B,iBAAA,CAAoB;UAkB1CvB,EAAA,CAAAK,SAAA,EAAuC;UAAvCL,EAAA,CAAAM,UAAA,SAAA2C,GAAA,CAAA5B,UAAA,IAAA4B,GAAA,CAAA3B,QAAA,CAAAiC,MAAA,KAAuC;;;qBA5B7DzD,YAAY,EAAA0D,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE3D,YAAY,EAAA4D,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}