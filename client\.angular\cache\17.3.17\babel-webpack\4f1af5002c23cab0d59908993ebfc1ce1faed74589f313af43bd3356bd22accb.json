{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from '../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ApiService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.apiUrl;\n  }\n  /**\n   * Effectue une requête GET\n   */\n  get(endpoint, params) {\n    let httpParams = new HttpParams();\n    if (params) {\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined) {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n    }\n    return this.http.get(`${this.baseUrl}${endpoint}`, {\n      params: httpParams\n    });\n  }\n  /**\n   * Effectue une requête POST\n   */\n  post(endpoint, data) {\n    return this.http.post(`${this.baseUrl}${endpoint}`, data);\n  }\n  /**\n   * Effectue une requête PUT\n   */\n  put(endpoint, data) {\n    return this.http.put(`${this.baseUrl}${endpoint}`, data);\n  }\n  /**\n   * Effectue une requête DELETE\n   */\n  delete(endpoint) {\n    return this.http.delete(`${this.baseUrl}${endpoint}`);\n  }\n  /**\n   * Construit l'URL complète pour un endpoint\n   */\n  getFullUrl(endpoint) {\n    return `${this.baseUrl}${endpoint}`;\n  }\n  static {\n    this.ɵfac = function ApiService_Factory(t) {\n      return new (t || ApiService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ApiService,\n      factory: ApiService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "environment", "ApiService", "constructor", "http", "baseUrl", "apiUrl", "get", "endpoint", "params", "httpParams", "Object", "keys", "for<PERSON>ach", "key", "undefined", "set", "toString", "post", "data", "put", "delete", "getFullUrl", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Site e-commerce\\client\\src\\app\\services\\api.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApiService {\n  private readonly baseUrl = environment.apiUrl;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Effectue une requête GET\n   */\n  get<T>(endpoint: string, params?: any): Observable<T> {\n    let httpParams = new HttpParams();\n    \n    if (params) {\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined) {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n    }\n\n    return this.http.get<T>(`${this.baseUrl}${endpoint}`, { params: httpParams });\n  }\n\n  /**\n   * Effectue une requête POST\n   */\n  post<T>(endpoint: string, data: any): Observable<T> {\n    return this.http.post<T>(`${this.baseUrl}${endpoint}`, data);\n  }\n\n  /**\n   * Effectue une requête PUT\n   */\n  put<T>(endpoint: string, data: any): Observable<T> {\n    return this.http.put<T>(`${this.baseUrl}${endpoint}`, data);\n  }\n\n  /**\n   * Effectue une requête DELETE\n   */\n  delete<T>(endpoint: string): Observable<T> {\n    return this.http.delete<T>(`${this.baseUrl}${endpoint}`);\n  }\n\n  /**\n   * Construit l'URL complète pour un endpoint\n   */\n  getFullUrl(endpoint: string): string {\n    return `${this.baseUrl}${endpoint}`;\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,WAAW,QAAQ,6BAA6B;;;AAKzD,OAAM,MAAOC,UAAU;EAGrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;EAEN;EAEvC;;;EAGAC,GAAGA,CAAIC,QAAgB,EAAEC,MAAY;IACnC,IAAIC,UAAU,GAAG,IAAIV,UAAU,EAAE;IAEjC,IAAIS,MAAM,EAAE;MACVE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QAChC,IAAIL,MAAM,CAACK,GAAG,CAAC,KAAK,IAAI,IAAIL,MAAM,CAACK,GAAG,CAAC,KAAKC,SAAS,EAAE;UACrDL,UAAU,GAAGA,UAAU,CAACM,GAAG,CAACF,GAAG,EAAEL,MAAM,CAACK,GAAG,CAAC,CAACG,QAAQ,EAAE,CAAC;;MAE5D,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACb,IAAI,CAACG,GAAG,CAAI,GAAG,IAAI,CAACF,OAAO,GAAGG,QAAQ,EAAE,EAAE;MAAEC,MAAM,EAAEC;IAAU,CAAE,CAAC;EAC/E;EAEA;;;EAGAQ,IAAIA,CAAIV,QAAgB,EAAEW,IAAS;IACjC,OAAO,IAAI,CAACf,IAAI,CAACc,IAAI,CAAI,GAAG,IAAI,CAACb,OAAO,GAAGG,QAAQ,EAAE,EAAEW,IAAI,CAAC;EAC9D;EAEA;;;EAGAC,GAAGA,CAAIZ,QAAgB,EAAEW,IAAS;IAChC,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAI,GAAG,IAAI,CAACf,OAAO,GAAGG,QAAQ,EAAE,EAAEW,IAAI,CAAC;EAC7D;EAEA;;;EAGAE,MAAMA,CAAIb,QAAgB;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,MAAM,CAAI,GAAG,IAAI,CAAChB,OAAO,GAAGG,QAAQ,EAAE,CAAC;EAC1D;EAEA;;;EAGAc,UAAUA,CAACd,QAAgB;IACzB,OAAO,GAAG,IAAI,CAACH,OAAO,GAAGG,QAAQ,EAAE;EACrC;;;uBAhDWN,UAAU,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAVxB,UAAU;MAAAyB,OAAA,EAAVzB,UAAU,CAAA0B,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}