import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { ContactForm, QuoteForm, ContactInfo, ContactResponse } from '../models/contact.interface';

@Injectable({
  providedIn: 'root'
})
export class ContactService {
  private readonly endpoint = '/contact';

  constructor(private apiService: ApiService) {}

  /**
   * Soumet une demande de contact
   */
  submitContact(contactData: ContactForm): Observable<ContactResponse> {
    return this.apiService.post<ContactResponse>(`${this.endpoint}/submit`, contactData);
  }

  /**
   * Demande un devis pour les services B2B
   */
  requestQuote(quoteData: QuoteForm): Observable<ContactResponse> {
    return this.apiService.post<ContactResponse>(`${this.endpoint}/quote`, quoteData);
  }

  /**
   * Récupère les informations de contact
   */
  getContactInfo(): Observable<ContactResponse> {
    return this.apiService.get<ContactResponse>(`${this.endpoint}/info`);
  }

  /**
   * Valide un email
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Valide un numéro de téléphone français
   */
  validatePhoneNumber(phone: string): boolean {
    const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Formate un numéro de téléphone
   */
  formatPhoneNumber(phone: string): string {
    // Supprime tous les caractères non numériques sauf le +
    const cleaned = phone.replace(/[^\d+]/g, '');
    
    // Si le numéro commence par 0, on le remplace par +33
    if (cleaned.startsWith('0')) {
      return '+33' + cleaned.substring(1);
    }
    
    // Si le numéro commence par 33, on ajoute le +
    if (cleaned.startsWith('33')) {
      return '+' + cleaned;
    }
    
    return cleaned;
  }

  /**
   * Envoie un message de contact (alias pour submitContact)
   */
  sendMessage(contactData: any): Observable<any> {
    return this.submitContact(contactData);
  }
}
