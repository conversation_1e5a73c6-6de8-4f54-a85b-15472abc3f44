import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ContactService } from '../../services/contact.service';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <section class="contact-section" [class.home-section]="isHomePage">
      <div class="container">
        <h2>{{ isHomePage ? 'Contactez-nous' : 'Nous Contacter' }}</h2>
        <p class="section-description">
          {{ isHomePage ? 'Une question ? N\'hésitez pas à nous écrire.' : 'Nous sommes là pour vous accompagner dans votre démarche de développement personnel.' }}
        </p>
        
        <div class="contact-content">
          <div class="contact-info" *ngIf="!isHomePage">
            <div class="info-item">
              <i class="fas fa-envelope"></i>
              <div>
                <h4>Email</h4>
                <p>contact&#64;disconnecttoconnect.fr</p>
              </div>
            </div>
            <div class="info-item">
              <i class="fas fa-phone"></i>
              <div>
                <h4>Téléphone</h4>
                <p>+33 1 23 45 67 89</p>
              </div>
            </div>
            <div class="info-item">
              <i class="fas fa-map-marker-alt"></i>
              <div>
                <h4>Adresse</h4>
                <p>123 Rue du Développement<br>75001 Paris, France</p>
              </div>
            </div>
          </div>
          
          <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="contact-form">
            <div class="form-group">
              <label for="name">Nom complet *</label>
              <input 
                type="text" 
                id="name" 
                formControlName="name"
                [class.error]="contactForm.get('name')?.invalid && contactForm.get('name')?.touched"
              >
              <div class="error-message" *ngIf="contactForm.get('name')?.invalid && contactForm.get('name')?.touched">
                Le nom est requis
              </div>
            </div>
            
            <div class="form-group">
              <label for="email">Email *</label>
              <input 
                type="email" 
                id="email" 
                formControlName="email"
                [class.error]="contactForm.get('email')?.invalid && contactForm.get('email')?.touched"
              >
              <div class="error-message" *ngIf="contactForm.get('email')?.invalid && contactForm.get('email')?.touched">
                <span *ngIf="contactForm.get('email')?.errors?.['required']">L'email est requis</span>
                <span *ngIf="contactForm.get('email')?.errors?.['email']">Format d'email invalide</span>
              </div>
            </div>
            
            <div class="form-group">
              <label for="subject">Sujet *</label>
              <input 
                type="text" 
                id="subject" 
                formControlName="subject"
                [class.error]="contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched"
              >
              <div class="error-message" *ngIf="contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched">
                Le sujet est requis
              </div>
            </div>
            
            <div class="form-group">
              <label for="message">Message *</label>
              <textarea 
                id="message" 
                formControlName="message" 
                rows="5"
                [class.error]="contactForm.get('message')?.invalid && contactForm.get('message')?.touched"
              ></textarea>
              <div class="error-message" *ngIf="contactForm.get('message')?.invalid && contactForm.get('message')?.touched">
                Le message est requis
              </div>
            </div>
            
            <button 
              type="submit" 
              class="btn btn-primary"
              [disabled]="contactForm.invalid || isSubmitting"
            >
              {{ isSubmitting ? 'Envoi en cours...' : 'Envoyer le message' }}
            </button>
            
            <div class="success-message" *ngIf="showSuccessMessage">
              Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.
            </div>
            
            <div class="error-message" *ngIf="showErrorMessage">
              Une erreur s'est produite lors de l'envoi. Veuillez réessayer.
            </div>
          </form>
        </div>
      </div>
    </section>
  `,
  styles: [`
    .contact-section {
      padding: 80px 0;
      background: #f8f9fa;
    }
    
    .home-section {
      padding: 60px 0;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    h2 {
      text-align: center;
      margin-bottom: 20px;
      color: #2c3e50;
      font-size: 2.5rem;
    }
    
    .section-description {
      text-align: center;
      margin-bottom: 40px;
      color: #666;
      font-size: 1.1rem;
    }
    
    .contact-content {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 40px;
      align-items: start;
    }
    
    @media (max-width: 768px) {
      .contact-content {
        grid-template-columns: 1fr;
      }
    }
    
    .contact-info {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 25px;
    }
    
    .info-item i {
      font-size: 1.5rem;
      color: #3498db;
      margin-right: 15px;
      width: 30px;
    }
    
    .info-item h4 {
      margin: 0 0 5px 0;
      color: #2c3e50;
    }
    
    .info-item p {
      margin: 0;
      color: #666;
    }
    
    .contact-form {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      color: #2c3e50;
      font-weight: 500;
    }
    
    input, textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 16px;
      transition: border-color 0.3s ease;
    }
    
    input:focus, textarea:focus {
      outline: none;
      border-color: #3498db;
    }
    
    input.error, textarea.error {
      border-color: #e74c3c;
    }
    
    .btn {
      padding: 12px 30px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s ease;
    }
    
    .btn-primary {
      background: #3498db;
      color: white;
    }
    
    .btn-primary:hover:not(:disabled) {
      background: #2980b9;
    }
    
    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    .error-message {
      color: #e74c3c;
      font-size: 14px;
      margin-top: 5px;
    }
    
    .success-message {
      color: #27ae60;
      background: #d5f4e6;
      padding: 15px;
      border-radius: 5px;
      margin-top: 20px;
    }
  `]
})
export class ContactComponent implements OnInit {
  @Input() isHomePage: boolean = false;
  
  contactForm: FormGroup;
  isSubmitting = false;
  showSuccessMessage = false;
  showErrorMessage = false;

  constructor(
    private fb: FormBuilder,
    private contactService: ContactService
  ) {
    this.contactForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      subject: ['', Validators.required],
      message: ['', Validators.required]
    });
  }

  ngOnInit(): void {}

  onSubmit(): void {
    if (this.contactForm.valid) {
      this.isSubmitting = true;
      this.showSuccessMessage = false;
      this.showErrorMessage = false;

      this.contactService.sendMessage(this.contactForm.value).subscribe({
        next: () => {
          this.showSuccessMessage = true;
          this.contactForm.reset();
          this.isSubmitting = false;
        },
        error: () => {
          this.showErrorMessage = true;
          this.isSubmitting = false;
        }
      });
    } else {
      // Marquer tous les champs comme touchés pour afficher les erreurs
      Object.keys(this.contactForm.controls).forEach(key => {
        this.contactForm.get(key)?.markAsTouched();
      });
    }
  }
}
