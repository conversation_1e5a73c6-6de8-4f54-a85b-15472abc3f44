import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ProductService } from '../../services/product.service';
import { Product } from '../../models/product.interface';

@Component({
  selector: 'app-product-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="product-detail" *ngIf="product">
      <div class="container">
        <nav class="breadcrumb">
          <a routerLink="/">Accueil</a>
          <span>/</span>
          <a routerLink="/products">Produits</a>
          <span>/</span>
          <span>{{ product.name }}</span>
        </nav>
        
        <div class="product-content">
          <div class="product-image">
            <img [src]="product.image" [alt]="product.name" />
          </div>
          
          <div class="product-info">
            <h1>{{ product.name }}</h1>
            <p class="product-category">{{ product.category }}</p>
            <p class="product-description">{{ product.description }}</p>
            
            <div class="product-details">
              <h3>Caractéristiques</h3>
              <ul>
                <li>Jeu de cartes de développement personnel</li>
                <li>Format pratique et transportable</li>
                <li>Illustrations inspirantes</li>
                <li>Guide d'utilisation inclus</li>
              </ul>
            </div>
            
            <div class="product-price">
              <span class="price">{{ product.price }}€</span>
              <span class="stock-status" [class.in-stock]="product.inStock" [class.out-of-stock]="!product.inStock">
                {{ product.inStock ? 'En stock' : 'Rupture de stock' }}
              </span>
            </div>
            
            <div class="product-actions">
              <button 
                class="btn btn-primary btn-large"
                [disabled]="!product.inStock"
                (click)="addToCart()"
              >
                {{ product.inStock ? 'Ajouter au panier' : 'Produit indisponible' }}
              </button>
              <button class="btn btn-outline" routerLink="/products">
                Retour aux produits
              </button>
            </div>
          </div>
        </div>
        
        <div class="product-tabs">
          <div class="tab-headers">
            <button 
              class="tab-header"
              [class.active]="activeTab === 'description'"
              (click)="activeTab = 'description'"
            >
              Description détaillée
            </button>
            <button 
              class="tab-header"
              [class.active]="activeTab === 'usage'"
              (click)="activeTab = 'usage'"
            >
              Mode d'emploi
            </button>
            <button 
              class="tab-header"
              [class.active]="activeTab === 'reviews'"
              (click)="activeTab = 'reviews'"
            >
              Avis clients
            </button>
          </div>
          
          <div class="tab-content">
            <div *ngIf="activeTab === 'description'" class="tab-panel">
              <h3>Description complète</h3>
              <p>
                Ce jeu de cartes de développement personnel a été conçu pour vous accompagner 
                dans votre parcours de croissance personnelle. Chaque carte contient des exercices 
                pratiques, des réflexions profondes et des conseils inspirants pour vous aider à 
                développer votre potentiel.
              </p>
              <p>
                Que vous soyez débutant ou expérimenté dans le développement personnel, 
                ces cartes s'adaptent à votre rythme et à vos besoins spécifiques.
              </p>
            </div>
            
            <div *ngIf="activeTab === 'usage'" class="tab-panel">
              <h3>Comment utiliser vos cartes</h3>
              <ol>
                <li>Choisissez un moment calme de votre journée</li>
                <li>Mélangez les cartes et tirez-en une au hasard</li>
                <li>Lisez attentivement le contenu de la carte</li>
                <li>Prenez le temps de réfléchir à la question ou l'exercice proposé</li>
                <li>Mettez en pratique les conseils dans votre quotidien</li>
              </ol>
            </div>
            
            <div *ngIf="activeTab === 'reviews'" class="tab-panel">
              <h3>Avis de nos clients</h3>
              <div class="review">
                <div class="review-rating">★★★★★</div>
                <p>"Un outil formidable pour le développement personnel. Je recommande vivement !"</p>
                <cite>- Marie L.</cite>
              </div>
              <div class="review">
                <div class="review-rating">★★★★☆</div>
                <p>"Très bien conçu, les exercices sont variés et inspirants."</p>
                <cite>- Pierre D.</cite>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="loading" *ngIf="!product && !error">
      <p>Chargement du produit...</p>
    </div>
    
    <div class="error" *ngIf="error">
      <div class="container">
        <h2>Produit non trouvé</h2>
        <p>Le produit que vous recherchez n'existe pas ou n'est plus disponible.</p>
        <button class="btn btn-primary" routerLink="/products">
          Retour aux produits
        </button>
      </div>
    </div>
  `,
  styles: [`
    .product-detail {
      padding: 100px 0 80px;
      min-height: 100vh;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    .breadcrumb {
      margin-bottom: 30px;
      color: #666;
    }
    
    .breadcrumb a {
      color: #3498db;
      text-decoration: none;
    }
    
    .breadcrumb span {
      margin: 0 10px;
    }
    
    .product-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;
      margin-bottom: 60px;
    }
    
    @media (max-width: 768px) {
      .product-content {
        grid-template-columns: 1fr;
      }
    }
    
    .product-image img {
      width: 100%;
      border-radius: 10px;
    }
    
    .product-info h1 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .product-category {
      color: #3498db;
      font-weight: 500;
      margin-bottom: 20px;
    }
    
    .product-description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 30px;
    }
    
    .product-details {
      margin-bottom: 30px;
    }
    
    .product-details h3 {
      color: #2c3e50;
      margin-bottom: 15px;
    }
    
    .product-details ul {
      list-style: none;
      padding: 0;
    }
    
    .product-details li {
      padding: 5px 0;
      color: #666;
    }
    
    .product-details li:before {
      content: "✓";
      color: #27ae60;
      margin-right: 10px;
    }
    
    .product-price {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .price {
      font-size: 2rem;
      font-weight: bold;
      color: #e74c3c;
    }
    
    .stock-status {
      padding: 5px 15px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
    }
    
    .in-stock {
      background: #d5f4e6;
      color: #27ae60;
    }
    
    .out-of-stock {
      background: #fadbd8;
      color: #e74c3c;
    }
    
    .product-actions {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }
    
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      text-align: center;
      transition: all 0.3s ease;
      font-size: 16px;
    }
    
    .btn-large {
      padding: 15px 30px;
      font-size: 18px;
    }
    
    .btn-primary {
      background: #3498db;
      color: white;
    }
    
    .btn-primary:hover:not(:disabled) {
      background: #2980b9;
    }
    
    .btn-outline {
      background: transparent;
      color: #3498db;
      border: 2px solid #3498db;
    }
    
    .btn-outline:hover {
      background: #3498db;
      color: white;
    }
    
    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    .product-tabs {
      background: white;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .tab-headers {
      display: flex;
      background: #f8f9fa;
    }
    
    .tab-header {
      flex: 1;
      padding: 15px 20px;
      border: none;
      background: transparent;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .tab-header.active {
      background: white;
      color: #3498db;
      border-bottom: 2px solid #3498db;
    }
    
    .tab-content {
      padding: 30px;
    }
    
    .review {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    
    .review-rating {
      color: #f39c12;
      margin-bottom: 10px;
    }
    
    .review cite {
      color: #666;
      font-style: italic;
    }
    
    .loading, .error {
      text-align: center;
      padding: 100px 0;
    }
    
    .error h2 {
      color: #e74c3c;
      margin-bottom: 20px;
    }
  `]
})
export class ProductDetailComponent implements OnInit {
  product: Product | null = null;
  error = false;
  activeTab = 'description';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private productService: ProductService
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadProduct(id);
    } else {
      this.error = true;
    }
  }

  private loadProduct(id: string): void {
    this.productService.getProduct(id).subscribe({
      next: (product: Product) => {
        this.product = product;
      },
      error: () => {
        this.error = true;
      }
    });
  }

  addToCart(): void {
    if (this.product) {
      // Logique d'ajout au panier à implémenter
      alert(`${this.product.name} ajouté au panier !`);
    }
  }
}
